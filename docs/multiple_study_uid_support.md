# 多StudyInstanceUID支持实现文档

## 概述

本文档详细说明了在DiagnosisReport组件中实现对患者多个StudyInstanceUID的支持，解决了患者可能有多次检查的情况下影像查看功能的问题。

## 问题背景

### 原有实现的局限性

1. **单一StudyInstanceUID假设**：
   - 原代码假设每个患者只有一个StudyInstanceUID
   - 只选择第一个返回的检查数据

2. **缺少检查选择机制**：
   - 用户无法选择查看不同的检查
   - 可能错过重要的检查数据

3. **检查匹配不准确**：
   - 没有根据检查号、检查类型等信息匹配最相关的检查
   - 可能显示错误的影像数据

## 解决方案

### 1. 数据结构扩展

**添加多StudyInstanceUID支持**：
```javascript
// 影像数据相关
const studyInstanceUid = ref('')           // 当前选中的StudyInstanceUID
const allStudyInstanceUids = ref([])       // 所有可用的StudyInstanceUID
const isLoadingStudyUid = ref(false)       // 加载状态
```

**StudyInstanceUID数据结构**：
```javascript
{
  studyInstanceUid: '1.2.840.113619.2.55.3.604688119.868.1234567890.123',
  studyDate: '20240730',                   // DICOM格式日期
  studyTime: '143022.123456',              // DICOM格式时间
  studyDescription: 'CT胸部平扫',           // 检查描述
  modality: 'CT',                          // 检查类型
  accessionNumber: 'ACC123456',            // 检查号
  index: 0                                 // 在原始数据中的索引
}
```

### 2. 智能检查选择算法

**多维度匹配策略**：

```javascript
const selectBestStudy = (studyUids) => {
  // 优先级评分系统
  const scoringCriteria = {
    examCodeMatch: 100,      // 检查号完全匹配
    modalityMatch: 50,       // 检查类型匹配
    studyDateMatch: 30,      // 检查日期匹配
    latestStudy: 10          // 最新检查
  }
  
  // 计算每个检查的匹配分数
  // 选择得分最高的检查
}
```

**匹配条件**：
1. **检查号匹配**（最高优先级）：
   - 比较`accessionNumber`与当前检查号
   - 完全匹配得100分

2. **检查类型匹配**：
   - 比较`modality`与当前检查类型
   - 匹配得50分

3. **检查日期匹配**：
   - 比较`studyDate`与当前检查日期
   - 匹配得30分

4. **最新检查优先**：
   - 如果没有其他匹配条件，选择最新的检查
   - 得10分

### 3. 用户界面增强

**检查选择器**：
```vue
<el-dropdown 
  v-if="allStudyInstanceUids.length > 1" 
  @command="switchStudy"
  trigger="click"
>
  <el-button size="small" type="primary" plain>
    选择检查 <el-icon><arrow-down /></el-icon>
  </el-button>
  <template #dropdown>
    <el-dropdown-menu>
      <el-dropdown-item 
        v-for="study in allStudyInstanceUids" 
        :key="study.studyInstanceUid"
        :command="study.studyInstanceUid"
      >
        <div class="study-item">
          <div class="study-title">
            {{ study.studyDescription || `检查 ${index + 1}` }}
          </div>
          <div class="study-info">
            <span>{{ formatStudyDate(study.studyDate) }}</span>
            <span>{{ study.modality }}</span>
            <span>{{ study.accessionNumber }}</span>
          </div>
        </div>
      </el-dropdown-item>
    </el-dropdown-menu>
  </template>
</el-dropdown>
```

**显示信息**：
- 检查描述或序号
- 检查日期（格式化显示）
- 检查类型（CT、MR、DR等）
- 检查号

### 4. URL构建优化

**支持多StudyInstanceUID的URL**：
```javascript
const buildViewerUrl = (studyId, options = {}) => {
  const { isFullscreen = false, allStudyUids = [] } = options
  
  // 基础参数
  const params = {
    studyInstanceUid: studyId,
    fullscreen: isFullscreen ? 'true' : undefined,
    mode: isFullscreen ? 'advanced' : undefined
  }
  
  // 添加所有StudyInstanceUID
  if (allStudyUids.length > 1) {
    params.allStudyInstanceUids = allStudyUids.join(',')
  }
  
  return `${baseUrl}?${new URLSearchParams(params).toString()}`
}
```

**URL参数说明**：
- `studyInstanceUid`: 主要显示的检查
- `allStudyInstanceUids`: 所有可用检查的逗号分隔列表
- 影像查看器可以根据这些参数提供检查切换功能

### 5. DICOM数据解析

**标准DICOM标签解析**：
```javascript
const extractStudyInfo = (studyData) => {
  return {
    studyInstanceUid: studyData['0020000D']?.Value?.[0],  // Study Instance UID
    studyDate: studyData['00080020']?.Value?.[0],         // Study Date
    studyTime: studyData['00080030']?.Value?.[0],         // Study Time
    studyDescription: studyData['00081030']?.Value?.[0],  // Study Description
    modality: studyData['00080060']?.Value?.[0],          // Modality
    accessionNumber: studyData['00080050']?.Value?.[0]    // Accession Number
  }
}
```

**DICOM标签对照表**：
| 标签 | 名称 | 用途 |
|------|------|------|
| 0020,000D | Study Instance UID | 检查唯一标识 |
| 0008,0020 | Study Date | 检查日期 |
| 0008,0030 | Study Time | 检查时间 |
| 0008,1030 | Study Description | 检查描述 |
| 0008,0060 | Modality | 检查类型 |
| 0008,0050 | Accession Number | 检查号 |

### 6. 日期时间处理

**DICOM日期格式转换**：
```javascript
// DICOM日期格式: YYYYMMDD
const formatStudyDate = (studyDate) => {
  if (!studyDate || studyDate.length !== 8) return studyDate
  
  const year = studyDate.substring(0, 4)
  const month = studyDate.substring(4, 6)
  const day = studyDate.substring(6, 8)
  return `${year}-${month}-${day}`
}

// DICOM时间格式: HHMMSS.FFFFFF
const parseStudyDateTime = (studyDate, studyTime) => {
  const year = parseInt(studyDate.substring(0, 4))
  const month = parseInt(studyDate.substring(4, 6)) - 1
  const day = parseInt(studyDate.substring(6, 8))
  
  let hour = 0, minute = 0, second = 0
  if (studyTime) {
    hour = parseInt(studyTime.substring(0, 2)) || 0
    minute = parseInt(studyTime.substring(2, 4)) || 0
    second = parseInt(studyTime.substring(4, 6)) || 0
  }
  
  return new Date(year, month, day, hour, minute, second)
}
```

## 工作流程

### 1. 数据获取流程
1. 调用`getStudyInstanceUid` API获取患者所有检查
2. 解析DICOM响应数据，提取所有有效的StudyInstanceUID
3. 使用智能选择算法选择最合适的检查
4. 保存所有检查信息供用户选择

### 2. 检查选择流程
1. 如果只有一个检查，直接使用
2. 如果有多个检查，显示选择器
3. 用户可以通过下拉菜单切换不同的检查
4. 切换时更新当前的StudyInstanceUID

### 3. 影像查看流程
1. 构建包含所有StudyInstanceUID的查看器URL
2. 传递主要检查和所有可用检查给查看器
3. 查看器可以提供检查间的切换功能

## 优势特性

### 1. 智能匹配
- 根据检查号、类型、日期等多维度匹配
- 自动选择最相关的检查
- 减少用户手动选择的需要

### 2. 用户友好
- 清晰的检查信息显示
- 直观的选择界面
- 实时的切换反馈

### 3. 数据完整性
- 保留所有可用的检查信息
- 支持查看器的多检查功能
- 避免数据丢失

### 4. 向后兼容
- 单检查情况下行为不变
- 现有功能完全保留
- 渐进式增强

## 测试场景

### 1. 单检查患者
- 验证原有功能正常工作
- 不显示检查选择器
- 直接查看唯一的检查

### 2. 多检查患者
- 显示检查选择器
- 智能选择最相关的检查
- 支持手动切换检查

### 3. 检查匹配测试
- 测试检查号匹配
- 测试检查类型匹配
- 测试日期匹配
- 测试最新检查选择

### 4. 边界情况
- 无检查数据
- 检查数据不完整
- API调用失败
- 网络异常

## 注意事项

1. **性能考虑**：
   - 避免重复API调用
   - 缓存检查信息
   - 优化数据解析

2. **用户体验**：
   - 提供加载状态反馈
   - 清晰的错误提示
   - 直观的选择界面

3. **数据准确性**：
   - 验证DICOM数据格式
   - 处理缺失字段
   - 确保StudyInstanceUID有效性

4. **兼容性**：
   - 支持不同的DICOM服务器
   - 处理各种数据格式
   - 保持向后兼容

## 总结

通过实现多StudyInstanceUID支持，DiagnosisReport组件现在能够：

1. **智能处理多检查**：自动选择最相关的检查
2. **提供用户选择**：支持手动切换不同检查
3. **保持数据完整**：保留所有可用的检查信息
4. **增强用户体验**：提供清晰的检查信息和选择界面

这确保了在患者有多次检查的情况下，医生能够查看到正确和完整的影像数据，提升了诊断工作流程的准确性和效率。

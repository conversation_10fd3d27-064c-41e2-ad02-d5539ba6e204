# 增强的SSE心跳机制实现文档

## 概述

本文档详细说明了为PACS系统会诊模块实现的增强心跳机制，解决了原有心跳机制存在的内存泄漏、资源浪费和连接状态管理等问题。

## 问题分析

### 原有心跳机制的问题

1. **内存泄漏**：每个用户连接都会创建独立的定时任务，连接断开时没有正确取消
2. **资源浪费**：为每个用户单独创建定时任务，效率低下
3. **缺乏响应机制**：只发送心跳，没有检测客户端响应
4. **连接状态不准确**：无法准确判断连接是否真正有效
5. **配置不匹配**：前后端心跳超时时间不一致

## 改进方案

### 1. 统一心跳管理

**后端实现**：
- 使用单个定时任务管理所有连接的心跳发送
- 避免为每个连接创建独立的定时任务
- 统一的心跳调度器管理

```java
// 统一心跳任务
heartbeatTask = heartbeatScheduler.scheduleWithFixedDelay(
    this::sendHeartbeatToAllConnections, 
    HEARTBEAT_INTERVAL, 
    HEARTBEAT_INTERVAL, 
    TimeUnit.MILLISECONDS
);
```

### 2. 心跳响应机制

**服务端发送心跳**：
```java
// 发送心跳消息
emitter.send(SseEmitter.event()
        .name("heartbeat")
        .data(createHeartbeatData(currentTime)));
```

**客户端响应心跳**：
```javascript
// 处理心跳并发送响应
const handleHeartbeat = (event) => {
  lastHeartbeat.value = Date.now()
  sendHeartbeatResponseToServer()
}
```

**服务端处理响应**：
```java
@PostMapping("/heartbeat")
public AjaxResult heartbeat() {
    Long userId = SecurityUtils.getUserId();
    sseConnectionService.handleHeartbeatResponse(userId);
    return success("心跳响应处理成功");
}
```

### 3. 连接状态跟踪

**状态管理**：
- `connectionLastActive`: 记录连接最后活跃时间
- `heartbeatLastResponse`: 记录最后心跳响应时间

**状态更新**：
```java
public void handleHeartbeatResponse(Long userId) {
    if (sseConnections.containsKey(userId)) {
        long currentTime = System.currentTimeMillis();
        heartbeatLastResponse.put(userId, currentTime);
        connectionLastActive.put(userId, currentTime);
    }
}
```

### 4. 自动清理机制

**定期清理无效连接**：
```java
cleanupTask = heartbeatScheduler.scheduleWithFixedDelay(
    this::cleanupInactiveConnections, 
    CONNECTION_CLEANUP_INTERVAL, 
    CONNECTION_CLEANUP_INTERVAL, 
    TimeUnit.MILLISECONDS
);
```

**清理逻辑**：
- 检查心跳响应超时的连接
- 发送超时通知后断开连接
- 清理相关状态数据

## 配置参数

### 后端配置

| 参数 | 值 | 说明 |
|------|----|----|
| `SSE_TIMEOUT` | 30分钟 | SSE连接超时时间 |
| `HEARTBEAT_INTERVAL` | 30秒 | 心跳发送间隔 |
| `HEARTBEAT_TIMEOUT` | 90秒 | 心跳响应超时时间（允许3次心跳失败） |
| `CONNECTION_CLEANUP_INTERVAL` | 60秒 | 连接清理检查间隔 |

### 前端配置

| 参数 | 值 | 说明 |
|------|----|----|
| `heartbeatInterval` | 30秒 | 心跳检测间隔 |
| `heartbeatTimeout` | 90秒 | 心跳超时判断时间 |
| `reconnectDelay` | 1秒 | 初始重连延迟 |
| `maxReconnectDelay` | 30秒 | 最大重连延迟 |

## 技术架构

### 后端组件

#### 1. SseConnectionService
- **职责**：SSE连接管理、心跳发送、状态跟踪
- **关键方法**：
  - `createConnection()`: 创建连接
  - `sendHeartbeatToAllConnections()`: 统一心跳发送
  - `handleHeartbeatResponse()`: 处理心跳响应
  - `cleanupInactiveConnections()`: 清理无效连接

#### 2. ReliableNotificationController
- **职责**：HTTP接口处理、心跳响应接收
- **关键接口**：
  - `GET /connect`: 建立SSE连接
  - `POST /heartbeat`: 处理心跳响应
  - `GET /statistics`: 获取连接统计
  - `GET /health`: 获取连接健康状态

### 前端组件

#### 1. ReliableNotification组件
- **职责**：SSE连接管理、心跳处理、自动重连
- **关键功能**：
  - 心跳接收和响应
  - 连接状态监控
  - 自动重连机制
  - 通知显示和确认

## 工作流程

### 1. 连接建立
1. 前端调用 `/connect` 接口建立SSE连接
2. 后端创建连接并初始化状态
3. 后端发送连接成功消息
4. 前端启动心跳监控

### 2. 心跳循环
1. 后端定时向所有连接发送心跳消息
2. 前端接收心跳并调用 `/heartbeat` 接口响应
3. 后端更新连接的最后响应时间
4. 前端监控心跳超时，超时则重连

### 3. 连接清理
1. 后端定期检查所有连接的心跳响应时间
2. 超时的连接发送超时通知并断开
3. 清理相关状态数据
4. 前端检测到断开后自动重连

## 优势特性

### 1. 性能优化
- **统一调度**：单个定时任务管理所有连接，减少系统开销
- **资源节约**：避免为每个连接创建独立线程
- **内存管理**：及时清理断开连接的状态数据

### 2. 可靠性提升
- **双向确认**：服务端发送心跳，客户端响应确认
- **状态准确**：准确跟踪连接的真实状态
- **自动恢复**：连接断开后自动重连

### 3. 监控能力
- **连接统计**：实时统计在线用户数量
- **健康检查**：提供连接健康状态查询
- **状态监控**：记录连接活跃时间和响应时间

### 4. 配置灵活
- **参数可调**：心跳间隔、超时时间等参数可配置
- **策略可选**：支持不同的重连策略
- **环境适配**：可根据网络环境调整参数

## 使用示例

### 前端使用
```javascript
// 导入组件
import ReliableNotification from '@/components/ReliableNotification'

// 在页面中使用
<ReliableNotification />
```

### 后端监控
```java
// 获取连接统计
Map<String, Object> stats = sseConnectionService.getConnectionStatistics();

// 获取健康状态
Map<String, Object> health = sseConnectionService.getConnectionHealth();

// 发送测试通知
boolean sent = sseConnectionService.sendCustomEvent(userId, "test", "测试消息");
```

## 注意事项

1. **网络环境**：在网络不稳定的环境中，可能需要调整心跳间隔和超时时间
2. **服务器负载**：大量连接时需要监控服务器资源使用情况
3. **浏览器兼容**：确保目标浏览器支持EventSource和相关API
4. **安全考虑**：心跳接口需要适当的权限控制

## 故障排查

### 常见问题
1. **心跳超时**：检查网络连接和服务器负载
2. **重连失败**：检查认证状态和权限配置
3. **内存泄漏**：监控连接数量和清理机制
4. **性能问题**：调整心跳间隔和清理频率

### 调试工具
- 浏览器开发者工具的Network面板
- 后端日志监控
- 连接统计接口
- 健康检查接口

## 总结

增强的心跳机制通过统一管理、双向确认、状态跟踪和自动清理等改进，显著提升了SSE连接的可靠性和性能。该机制为PACS系统的实时通知功能提供了稳定可靠的基础设施。

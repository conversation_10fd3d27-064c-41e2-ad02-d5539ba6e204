# 手动检查选择功能实现文档

## 概述

本文档详细说明了在DiagnosisReport组件中实现手动检查选择功能，当患者有多个StudyInstanceUID时，让医生主动选择要查看的检查，而不是系统自动选择。

## 设计理念

### 用户体验优先
- **医生决策权**：让医生根据临床需要选择最相关的检查
- **信息透明**：显示所有可用检查的详细信息
- **操作简便**：提供直观的选择界面和快速查看功能

### 临床工作流程
- **多检查场景**：患者可能在不同时间进行多次检查
- **检查对比**：医生可能需要对比不同时间的检查结果
- **诊断准确性**：确保查看的是正确的检查影像

## 功能实现

### 1. 数据结构设计

**响应式数据**：
```javascript
// 影像数据相关
const studyInstanceUid = ref('')           // 当前选中的StudyInstanceUID
const allStudyInstanceUids = ref([])       // 所有可用的StudyInstanceUID
const isLoadingStudyUid = ref(false)       // 加载状态
const studySelectionVisible = ref(false)   // 检查选择对话框显示状态
const selectedStudyForViewing = ref('')    // 表格中选中的检查
```

**检查数据结构**：
```javascript
{
  studyInstanceUid: '1.2.840.113619.2.55.3.604688119.868.1234567890.123',
  studyDate: '20240730',                   // DICOM格式日期 YYYYMMDD
  studyTime: '143022.123456',              // DICOM格式时间 HHMMSS.FFFFFF
  studyDescription: 'CT胸部平扫',           // 检查描述
  modality: 'CT',                          // 检查类型
  accessionNumber: 'ACC123456',            // 检查号
  index: 0                                 // 在原始数据中的索引
}
```

### 2. 工作流程设计

#### 2.1 数据获取流程
```mermaid
graph TD
    A[组件挂载] --> B[预加载检查数据]
    B --> C{检查数据获取成功?}
    C -->|是| D[保存所有检查信息]
    C -->|否| E[显示错误提示]
    D --> F{检查数量}
    F -->|1个| G[直接设置为当前检查]
    F -->|多个| H[等待用户选择]
```

#### 2.2 用户选择流程
```mermaid
graph TD
    A[用户点击查看影像] --> B{是否已获取检查数据?}
    B -->|否| C[获取检查数据]
    B -->|是| D{检查数量}
    C --> D
    D -->|1个| E[直接打开影像查看器]
    D -->|多个| F[显示检查选择对话框]
    F --> G[用户选择检查]
    G --> H[确认选择]
    H --> I[打开影像查看器]
```

### 3. 用户界面设计

#### 3.1 检查选择对话框
```vue
<el-dialog
  v-model="studySelectionVisible"
  title="选择要查看的检查"
  width="800px"
  :close-on-click-modal="false"
>
  <div class="study-selection-content">
    <p class="selection-tip">该患者有多个检查，请选择要查看的检查：</p>
    
    <el-table
      :data="allStudyInstanceUids"
      @current-change="handleStudySelectionChange"
      highlight-current-row
    >
      <!-- 表格列定义 -->
    </el-table>
  </div>
  
  <template #footer>
    <el-button @click="studySelectionVisible = false">取消</el-button>
    <el-button 
      type="primary" 
      @click="confirmSelectedStudy"
      :disabled="!selectedStudyForViewing"
    >
      确定查看
    </el-button>
  </template>
</el-dialog>
```

#### 3.2 表格列设计
| 列名 | 宽度 | 说明 |
|------|------|------|
| 序号 | 60px | 显示检查序号 |
| 检查描述 | 150px+ | 显示检查名称或描述 |
| 检查日期 | 120px | 格式化显示日期 |
| 检查时间 | 100px | 格式化显示时间 |
| 检查类型 | 80px | 显示Modality（CT、MR等） |
| 检查号 | 120px | 显示Accession Number |
| 操作 | 100px | 快速查看按钮 |

### 4. 核心方法实现

#### 4.1 数据获取方法
```javascript
const fetchStudyInstanceUid = async () => {
  // 获取患者所有检查数据
  // 解析DICOM标签信息
  // 保存到allStudyInstanceUids
  // 如果只有一个检查，直接设置为当前检查
  // 如果有多个检查，等待用户选择
}
```

#### 4.2 用户选择处理
```javascript
// 表格行选择变化
const handleStudySelectionChange = (currentRow) => {
  if (currentRow) {
    selectedStudyForViewing.value = currentRow.studyInstanceUid
  }
}

// 直接选择并查看
const selectStudyAndView = (studyUid) => {
  // 设置当前检查
  // 关闭选择对话框
  // 打开影像查看器
}

// 确认选择
const confirmSelectedStudy = () => {
  if (selectedStudyForViewing.value) {
    selectStudyAndView(selectedStudyForViewing.value)
  }
}
```

#### 4.3 影像查看逻辑
```javascript
const viewImages = async () => {
  // 验证患者数据
  // 获取检查数据（如果还没有）
  // 检查数量判断
  if (allStudyInstanceUids.value.length > 1 && !studyInstanceUid.value) {
    // 显示选择对话框
    studySelectionVisible.value = true
    return
  }
  // 直接打开查看器
  fullscreenViewerVisible.value = true
}
```

### 5. 数据格式化

#### 5.1 日期格式化
```javascript
const formatStudyDate = (studyDate) => {
  // DICOM格式: YYYYMMDD -> YYYY-MM-DD
  if (!studyDate || studyDate.length !== 8) return studyDate
  
  const year = studyDate.substring(0, 4)
  const month = studyDate.substring(4, 6)
  const day = studyDate.substring(6, 8)
  return `${year}-${month}-${day}`
}
```

#### 5.2 时间格式化
```javascript
const formatStudyTime = (studyTime) => {
  // DICOM格式: HHMMSS.FFFFFF -> HH:MM:SS
  if (!studyTime || studyTime.length < 6) return studyTime || '-'
  
  const hour = studyTime.substring(0, 2)
  const minute = studyTime.substring(2, 4)
  const second = studyTime.substring(4, 6)
  return `${hour}:${minute}:${second}`
}
```

### 6. 样式设计

#### 6.1 对话框样式
```css
.study-selection-content {
  padding: 0;
}

.selection-tip {
  margin: 0 0 16px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}
```

#### 6.2 表格样式
```css
.study-selection-content .el-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.study-selection-content .el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

.study-selection-content .el-table .current-row {
  background-color: #ecf5ff;
}
```

## 用户交互设计

### 1. 操作方式
- **表格行点击**：点击表格行选中检查
- **快速查看按钮**：直接选择并查看检查
- **确定按钮**：确认表格中选中的检查

### 2. 状态反馈
- **高亮显示**：当前选中的表格行高亮显示
- **按钮状态**：未选择时确定按钮禁用
- **成功提示**：选择检查后显示成功消息

### 3. 错误处理
- **数据加载失败**：显示错误提示，允许重试
- **未选择检查**：提示用户先选择检查
- **网络异常**：提供友好的错误信息

## 优势特性

### 1. 医生主导
- **决策权在医生**：不做自动选择，让医生根据临床需要决定
- **信息完整**：显示所有检查的详细信息
- **选择灵活**：支持多种选择方式

### 2. 用户体验
- **界面直观**：清晰的表格展示和操作按钮
- **操作简便**：支持点击选择和快速查看
- **反馈及时**：实时的状态反馈和提示信息

### 3. 数据准确
- **信息详细**：显示检查日期、时间、类型等关键信息
- **格式友好**：人性化的日期时间格式显示
- **数据完整**：保留所有DICOM标签信息

### 4. 扩展性
- **易于扩展**：可以添加更多检查信息列
- **配置灵活**：可以调整表格列的显示和排序
- **功能丰富**：可以添加检查对比等高级功能

## 测试场景

### 1. 单检查场景
- 患者只有一个检查
- 应该直接打开影像查看器
- 不显示选择对话框

### 2. 多检查场景
- 患者有多个检查
- 显示检查选择对话框
- 用户可以选择任意检查查看

### 3. 交互测试
- 表格行点击选择
- 快速查看按钮
- 确定按钮状态变化
- 取消操作

### 4. 边界情况
- 无检查数据
- 检查数据不完整
- 网络异常
- 用户取消操作

## 注意事项

### 1. 性能考虑
- **预加载数据**：组件挂载时预加载检查信息
- **缓存机制**：避免重复API调用
- **表格优化**：大量检查时的性能优化

### 2. 用户体验
- **加载状态**：显示数据加载状态
- **错误处理**：友好的错误提示和恢复机制
- **操作引导**：清晰的操作提示和帮助信息

### 3. 数据安全
- **输入验证**：验证检查数据的有效性
- **错误恢复**：处理API调用失败的情况
- **状态管理**：正确管理组件状态

## 总结

通过实现手动检查选择功能，DiagnosisReport组件现在能够：

1. **尊重医生决策**：让医生根据临床需要选择检查
2. **提供完整信息**：显示所有可用检查的详细信息
3. **简化操作流程**：直观的选择界面和快速操作
4. **确保数据准确**：避免自动选择可能的错误

这种设计更符合临床工作流程，提升了医生的工作效率和诊断准确性。

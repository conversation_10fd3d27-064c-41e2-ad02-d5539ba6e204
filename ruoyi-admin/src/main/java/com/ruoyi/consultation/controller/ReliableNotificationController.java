package com.ruoyi.consultation.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.consultation.service.ReliableNotificationService;
import com.ruoyi.consultation.service.SseConnectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 可靠通知控制器
 * 负责处理SSE连接、心跳响应、通知确认等功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/consultation/reliable-notification")
public class ReliableNotificationController extends BaseController {

    @Autowired
    private SseConnectionService sseConnectionService;

    @Autowired
    private ReliableNotificationService reliableNotificationService;

    /**
     * 建立SSE连接
     *
     * @param request HTTP请求
     * @return SSE连接对象
     */
    @GetMapping("/connect")
    public SseEmitter connect(HttpServletRequest request) {
        try {
            Long userId = SecurityUtils.getUserId();
            log.info("用户 {} 请求建立SSE连接", userId);

            // 创建SSE连接
            SseEmitter emitter = sseConnectionService.createConnection(userId);

            // 发送离线通知
            reliableNotificationService.sendOfflineNotificationsToUser(userId);

            log.info("用户 {} 的SSE连接建立成功", userId);
            return emitter;

        } catch (Exception e) {
            log.error("建立SSE连接失败", e);
            throw new RuntimeException("建立SSE连接失败: " + e.getMessage());
        }
    }

    /**
     * 处理心跳响应
     *
     * @return 操作结果
     */
    @PostMapping("/heartbeat")
    public AjaxResult heartbeat() {
        try {
            Long userId = SecurityUtils.getUserId();
            sseConnectionService.handleHeartbeatResponse(userId);
            
            log.debug("处理用户 {} 的心跳响应", userId);
            return success("心跳响应处理成功");

        } catch (Exception e) {
            log.error("处理心跳响应失败", e);
            return error("处理心跳响应失败: " + e.getMessage());
        }
    }

    /**
     * 确认通知已收到
     *
     * @param notificationId 通知ID
     * @return 操作结果
     */
    @PostMapping("/acknowledge/{notificationId}")
    public AjaxResult acknowledgeNotification(@PathVariable Long notificationId) {
        try {
            Long userId = SecurityUtils.getUserId();
            boolean result = reliableNotificationService.acknowledgeNotification(notificationId, userId);
            
            if (result) {
                log.info("用户 {} 确认通知 {} 成功", userId, notificationId);
                return success("通知确认成功");
            } else {
                log.warn("用户 {} 确认通知 {} 失败", userId, notificationId);
                return error("通知确认失败");
            }

        } catch (Exception e) {
            log.error("确认通知失败，通知ID: {}", notificationId, e);
            return error("确认通知失败: " + e.getMessage());
        }
    }

    /**
     * 获取离线通知列表
     *
     * @return 离线通知列表
     */
    @GetMapping("/offline-notifications")
    public AjaxResult getOfflineNotifications() {
        try {
            Long userId = SecurityUtils.getUserId();
            var notifications = reliableNotificationService.getOfflineNotifications(userId);
            
            log.info("用户 {} 查询离线通知，数量: {}", userId, notifications.size());
            return success(notifications);

        } catch (Exception e) {
            log.error("获取离线通知失败", e);
            return error("获取离线通知失败: " + e.getMessage());
        }
    }

    /**
     * 重发通知
     *
     * @param notificationId 通知ID
     * @return 操作结果
     */
    @PostMapping("/resend/{notificationId}")
    @PreAuthorize("@ss.hasPermi('consultation:notification:resend')")
    public AjaxResult resendNotification(@PathVariable Long notificationId) {
        try {
            boolean result = reliableNotificationService.resendNotification(notificationId);
            
            if (result) {
                log.info("重发通知 {} 成功", notificationId);
                return success("重发通知成功");
            } else {
                log.warn("重发通知 {} 失败", notificationId);
                return error("重发通知失败");
            }

        } catch (Exception e) {
            log.error("重发通知失败，通知ID: {}", notificationId, e);
            return error("重发通知失败: " + e.getMessage());
        }
    }

    /**
     * 获取连接统计信息
     *
     * @return 连接统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("@ss.hasPermi('consultation:notification:statistics')")
    public AjaxResult getConnectionStatistics() {
        try {
            Map<String, Object> stats = sseConnectionService.getConnectionStatistics();
            return success(stats);

        } catch (Exception e) {
            log.error("获取连接统计信息失败", e);
            return error("获取连接统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取连接健康状态
     *
     * @return 连接健康状态
     */
    @GetMapping("/health")
    @PreAuthorize("@ss.hasPermi('consultation:notification:health')")
    public AjaxResult getConnectionHealth() {
        try {
            Map<String, Object> health = sseConnectionService.getConnectionHealth();
            return success(health);

        } catch (Exception e) {
            log.error("获取连接健康状态失败", e);
            return error("获取连接健康状态失败: " + e.getMessage());
        }
    }

    /**
     * 向指定用户发送测试通知
     *
     * @param userId 用户ID
     * @param message 消息内容
     * @return 操作结果
     */
    @PostMapping("/test-send/{userId}")
    @PreAuthorize("@ss.hasPermi('consultation:notification:test')")
    public AjaxResult sendTestNotification(@PathVariable Long userId, @RequestBody Map<String, String> message) {
        try {
            String content = message.get("content");
            boolean result = sseConnectionService.sendCustomEvent(userId, "test-notification", content);
            
            if (result) {
                log.info("向用户 {} 发送测试通知成功", userId);
                return success("测试通知发送成功");
            } else {
                log.warn("向用户 {} 发送测试通知失败，用户可能离线", userId);
                return error("测试通知发送失败，用户可能离线");
            }

        } catch (Exception e) {
            log.error("发送测试通知失败，用户ID: {}", userId, e);
            return error("发送测试通知失败: " + e.getMessage());
        }
    }

    /**
     * 广播测试通知给所有在线用户
     *
     * @param message 消息内容
     * @return 操作结果
     */
    @PostMapping("/broadcast-test")
    @PreAuthorize("@ss.hasPermi('consultation:notification:broadcast')")
    public AjaxResult broadcastTestNotification(@RequestBody Map<String, String> message) {
        try {
            String content = message.get("content");
            int count = sseConnectionService.broadcastNotification(content);
            
            log.info("广播测试通知完成，成功发送给 {} 个用户", count);
            return success("广播测试通知完成，成功发送给 " + count + " 个用户");

        } catch (Exception e) {
            log.error("广播测试通知失败", e);
            return error("广播测试通知失败: " + e.getMessage());
        }
    }

    /**
     * 获取在线用户数量
     *
     * @return 在线用户数量
     */
    @GetMapping("/online-count")
    public AjaxResult getOnlineCount() {
        try {
            int count = sseConnectionService.getOnlineUserCount();
            return success(count);

        } catch (Exception e) {
            log.error("获取在线用户数量失败", e);
            return error("获取在线用户数量失败: " + e.getMessage());
        }
    }

    /**
     * 强制断开指定用户的连接
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    @PostMapping("/disconnect/{userId}")
    @PreAuthorize("@ss.hasPermi('consultation:notification:disconnect')")
    public AjaxResult disconnectUser(@PathVariable Long userId) {
        try {
            sseConnectionService.removeConnection(userId);
            log.info("强制断开用户 {} 的连接", userId);
            return success("用户连接已断开");

        } catch (Exception e) {
            log.error("断开用户连接失败，用户ID: {}", userId, e);
            return error("断开用户连接失败: " + e.getMessage());
        }
    }
}

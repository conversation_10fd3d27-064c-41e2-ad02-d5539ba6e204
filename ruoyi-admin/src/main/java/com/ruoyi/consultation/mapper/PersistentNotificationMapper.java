package com.ruoyi.consultation.mapper;

import com.ruoyi.consultation.domain.PersistentNotification;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 持久化通知Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface PersistentNotificationMapper {

    /**
     * 查询持久化通知
     *
     * @param id 持久化通知主键
     * @return 持久化通知
     */
    public PersistentNotification selectPersistentNotificationById(Long id);

    /**
     * 查询持久化通知列表
     *
     * @param persistentNotification 持久化通知
     * @return 持久化通知集合
     */
    public List<PersistentNotification> selectPersistentNotificationList(PersistentNotification persistentNotification);

    /**
     * 新增持久化通知
     *
     * @param persistentNotification 持久化通知
     * @return 结果
     */
    public int insertPersistentNotification(PersistentNotification persistentNotification);

    /**
     * 修改持久化通知
     *
     * @param persistentNotification 持久化通知
     * @return 结果
     */
    public int updatePersistentNotification(PersistentNotification persistentNotification);

    /**
     * 删除持久化通知
     *
     * @param id 持久化通知主键
     * @return 结果
     */
    public int deletePersistentNotificationById(Long id);

    /**
     * 批量删除持久化通知
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePersistentNotificationByIds(Long[] ids);

    /**
     * 查询用户的待处理通知
     *
     * @param userId 用户ID
     * @return 通知列表
     */
    public List<PersistentNotification> selectPendingNotificationsByUserId(@Param("userId") Long userId);

    /**
     * 查询已发送但未确认的通知
     *
     * @return 通知列表
     */
    public List<PersistentNotification> selectSentNotifications();

    /**
     * 查询需要重试的通知
     *
     * @return 通知列表
     */
    public List<PersistentNotification> selectRetryNotifications();

    /**
     * 更新通知状态
     *
     * @param id 通知ID
     * @param status 状态
     * @param errorMessage 错误信息
     * @return 结果
     */
    public int updateNotificationStatus(@Param("id") Long id, 
                                      @Param("status") String status, 
                                      @Param("errorMessage") String errorMessage);

    /**
     * 增加重试次数
     *
     * @param id 通知ID
     * @return 结果
     */
    public int incrementRetryCount(@Param("id") Long id);

    /**
     * 标记通知为已送达
     *
     * @param id 通知ID
     * @return 结果
     */
    public int markAsDelivered(@Param("id") Long id);

    /**
     * 删除过期通知
     *
     * @param retentionDays 保留天数
     * @return 删除数量
     */
    public int deleteExpiredNotifications(@Param("retentionDays") int retentionDays);

    /**
     * 查询用户通知统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    public java.util.Map<String, Object> selectNotificationStatistics(@Param("userId") Long userId);

    /**
     * 查询高优先级待处理通知
     *
     * @param limit 限制数量
     * @return 通知列表
     */
    public List<PersistentNotification> selectHighPriorityNotifications(@Param("limit") int limit);

    /**
     * 批量更新通知状态
     *
     * @param ids 通知ID列表
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateNotificationStatus(@Param("ids") List<Long> ids, @Param("status") String status);

    /**
     * 查询用户最近的通知
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 通知列表
     */
    public List<PersistentNotification> selectRecentNotificationsByUserId(@Param("userId") Long userId, 
                                                                         @Param("limit") int limit);

    /**
     * 查询失败的通知（用于告警）
     *
     * @param hours 小时数
     * @return 通知列表
     */
    public List<PersistentNotification> selectFailedNotificationsInHours(@Param("hours") int hours);

    /**
     * 统计通知发送情况
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    public java.util.Map<String, Object> selectNotificationSendStatistics(@Param("startTime") java.util.Date startTime,
                                                                         @Param("endTime") java.util.Date endTime);

    /**
     * 查询用户未送达的通知（包括待发送和已发送但未确认的）
     *
     * @param userId 用户ID
     * @return 通知列表
     */
    public List<PersistentNotification> selectUndeliveredNotificationsByUserId(@Param("userId") Long userId);

    /**
     * 清理用户的所有通知
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int clearUserNotifications(@Param("userId") Long userId);
}

package com.ruoyi.consultation.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.consultation.service.ConsultationAuditService;
import com.ruoyi.consultation.service.ConsultationAsyncService;
import com.ruoyi.consultation.service.NotificationSendService;
import com.ruoyi.consultation.mapper.ConsultationAuditLogMapper;
import com.ruoyi.consultation.mapper.ConsultationNotificationMapper;
import com.ruoyi.datasync.domain.PacsPatientStudy;
import com.ruoyi.datasync.mapper.PacsPatientStudyMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.consultation.mapper.ConsultationRequestMapper;
import com.ruoyi.consultation.domain.ConsultationRequest;
import com.ruoyi.consultation.service.IConsultationRequestService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

/**
 * 会诊申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
@Slf4j
public class ConsultationRequestServiceImpl implements IConsultationRequestService {
    @Autowired
    private ConsultationRequestMapper consultationRequestMapper;

    @Autowired
    private PacsPatientStudyMapper pacsPatientStudyMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private NotificationSendService notificationSendService;

    @Autowired
    private ConsultationAuditService auditService;

    @Autowired
    private ConsultationAuditLogMapper auditLogMapper;

    @Autowired(required = false)
    private ConsultationNotificationMapper notificationMapper;

    @Autowired
    private ConsultationAsyncService asyncService;

    /**
     * 查询会诊申请
     *
     * @param id 会诊申请主键
     * @return 会诊申请
     */
    @Override
    public ConsultationRequest selectConsultationRequestById(Long id) {
        ConsultationRequest consultation = consultationRequestMapper.selectConsultationRequestById(id);
        if (consultation != null) {
            // 加载关联数据
            loadRelatedData(consultation);
        }
        return consultation;
    }

    /**
     * 查询会诊申请列表
     *
     * @param consultationRequest 会诊申请
     * @return 会诊申请
     */
    @Override
    public List<ConsultationRequest> selectConsultationRequestList(ConsultationRequest consultationRequest) {
        List<ConsultationRequest> list = consultationRequestMapper.selectConsultationRequestList(consultationRequest);
        // 批量加载关联数据
        for (ConsultationRequest consultation : list) {
            loadRelatedData(consultation);
        }
        return list;
    }

    /**
     * 新增会诊申请
     *
     * @param consultationRequest 会诊申请
     * @return 结果
     */
    @Override
    @Transactional
    public int insertConsultationRequest(ConsultationRequest consultationRequest) {
        long startTime = System.currentTimeMillis();

        consultationRequest.setCreateTime(DateUtils.getNowDate());
        consultationRequest.setCreateBy(SecurityUtils.getUsername());

        // 设置申请时间
        consultationRequest.setRequestTime(new Date());

        // 设置医院信息
        long hospitalInfoStart = System.currentTimeMillis();
        setHospitalInfo(consultationRequest);
        long hospitalInfoTime = System.currentTimeMillis() - hospitalInfoStart;
        log.debug("设置医院信息耗时: {}ms", hospitalInfoTime);

        // 插入数据库
        long insertStart = System.currentTimeMillis();
        int rows = consultationRequestMapper.insertConsultationRequest(consultationRequest);
        long insertTime = System.currentTimeMillis() - insertStart;
        log.debug("数据库插入耗时: {}ms", insertTime);

        // 异步记录审计日志，避免阻塞主流程
        asyncService.logCreateConsultationAsync(consultationRequest);

        long totalTime = System.currentTimeMillis() - startTime;
        log.debug("新增会诊申请总耗时: {}ms", totalTime);

        return rows;
    }

    /**
     * 修改会诊申请
     *
     * @param consultationRequest 会诊申请
     * @return 结果
     */
    @Override
    @Transactional
    public int updateConsultationRequest(ConsultationRequest consultationRequest) {
        // 获取原始数据用于审计
        ConsultationRequest oldConsultation = consultationRequestMapper.selectConsultationRequestById(consultationRequest.getId());
        
        consultationRequest.setUpdateTime(DateUtils.getNowDate());
        consultationRequest.setUpdateBy(SecurityUtils.getUsername());
        
        int rows = consultationRequestMapper.updateConsultationRequest(consultationRequest);
        
        // 记录审计日志
        if (oldConsultation != null) {
            auditService.logUpdateConsultation(oldConsultation, consultationRequest);
        }
        
        return rows;
    }

    /**
     * 批量删除会诊申请
     *
     * @param ids 需要删除的会诊申请主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteConsultationRequestByIds(Long[] ids) {
        int deletedCount = 0;
        for (Long id : ids) {
            if (deleteConsultationRequestById(id) > 0) {
                deletedCount++;
            }
        }
        return deletedCount;
    }

    /**
     * 删除会诊申请信息
     *
     * @param id 会诊申请主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteConsultationRequestById(Long id) {
        try {
            log.info("开始删除会诊申请，ID: {}", id);

            // 1. 检查记录是否存在
            ConsultationRequest consultation = consultationRequestMapper.selectConsultationRequestById(id);
            if (consultation == null) {
                log.warn("要删除的会诊申请不存在，ID: {}", id);
                return 0;
            }

            // 2. 删除相关的审计日志记录
            int auditLogCount = auditLogMapper.deleteConsultationAuditLogByConsultationId(id);
            log.info("删除审计日志记录数量: {}", auditLogCount);

            // 3. 删除相关的通知记录（如果存在）
            if (notificationMapper != null) {
                int notificationCount = notificationMapper.deleteConsultationNotificationByConsultationId(id);
                log.info("删除通知记录数量: {}", notificationCount);
            }

            // 4. 最后删除会诊申请记录
            int result = consultationRequestMapper.deleteConsultationRequestById(id);
            log.info("删除会诊申请记录结果: {}", result);

            return result;
        } catch (Exception e) {
            log.error("删除会诊申请失败，ID: {}", id, e);
            throw new RuntimeException("删除会诊申请失败：" + e.getMessage());
        }
    }

    /**
     * 创建会诊申请
     *
     * @param consultationRequest 会诊申请
     * @return 申请编号
     */
    @Override
    @Transactional
    public String createConsultationRequest(ConsultationRequest consultationRequest) {
        long startTime = System.currentTimeMillis();
        log.info("开始创建会诊申请，患者ID: {}", consultationRequest.getPatientId());

        try {
            // 生成申请编号
            String requestNo = generateRequestNo();
            consultationRequest.setRequestNo(requestNo);

            // 设置申请人信息
            consultationRequest.setRequesterId(SecurityUtils.getUserId());
            consultationRequest.setRequesterName(SecurityUtils.getUsername());

            // 设置默认状态
            consultationRequest.setStatus("PENDING");

            // 获取会诊医生信息
            if (consultationRequest.getConsultantId() != null) {
                long consultantQueryStart = System.currentTimeMillis();
                var consultant = userService.selectUserById(consultationRequest.getConsultantId());
                long consultantQueryTime = System.currentTimeMillis() - consultantQueryStart;
                log.info("查询会诊医生信息耗时: {}ms", consultantQueryTime);

                if (consultant != null) {
                    consultationRequest.setConsultantName(consultant.getNickName());
                    consultationRequest.setConsultantPhone(consultant.getPhonenumber());
                }
            }

            // 保存申请
            long saveStart = System.currentTimeMillis();
            insertConsultationRequest(consultationRequest);
            long saveTime = System.currentTimeMillis() - saveStart;
            log.info("保存会诊申请耗时: {}ms", saveTime);

            // 异步发送通知，避免阻塞主流程
            Long notificationId = notificationSendService.sendConsultationRequestNotification(consultationRequest);
            if (notificationId != null) {
                log.info("会诊申请通知发送成功，通知ID: {}", notificationId);
            } else {
                log.warn("会诊申请通知发送失败");
            }

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("创建会诊申请完成，申请编号: {}, 总耗时: {}ms", requestNo, totalTime);

            return requestNo;

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("创建会诊申请失败，耗时: {}ms", totalTime, e);
            throw e;
        }
    }

    /**
     * 接受会诊申请
     *
     * @param id 会诊申请ID
     * @param acceptReason 接受原因
     * @return 结果
     */
    @Override
    @Transactional
    public boolean acceptConsultation(Long id, String acceptReason) {
        ConsultationRequest consultation = consultationRequestMapper.selectConsultationRequestById(id);
        if (consultation == null || !consultation.canAccept()) {
            return false;
        }
        
        // 更新状态
        consultation.setStatus("ACCEPTED");
        consultation.setResponseTime(new Date());
        consultation.setUpdateTime(DateUtils.getNowDate());
        consultation.setUpdateBy(SecurityUtils.getUsername());
        
        int rows = consultationRequestMapper.updateConsultationRequest(consultation);
        if (rows > 0) {
            // 记录审计日志
            auditService.logAcceptConsultation(consultation, acceptReason);
            
            // 异步发送通知
            asyncService.sendConsultationAcceptedNotificationAsync(consultation);
            
            return true;
        }
        
        return false;
    }

    /**
     * 拒绝会诊申请
     *
     * @param id 会诊申请ID
     * @param rejectReason 拒绝原因
     * @return 结果
     */
    @Override
    @Transactional
    public boolean rejectConsultation(Long id, String rejectReason) {
        ConsultationRequest consultation = consultationRequestMapper.selectConsultationRequestById(id);
        if (consultation == null || !consultation.canReject()) {
            return false;
        }
        
        // 更新状态
        consultation.setStatus("REJECTED");
        consultation.setResponseTime(new Date());
        consultation.setUpdateTime(DateUtils.getNowDate());
        consultation.setUpdateBy(SecurityUtils.getUsername());
        
        int rows = consultationRequestMapper.updateConsultationRequest(consultation);
        if (rows > 0) {
            // 记录审计日志
            auditService.logRejectConsultation(consultation, rejectReason);
            
            // 异步发送通知
            asyncService.sendConsultationRejectedNotificationAsync(consultation, rejectReason);
            
            return true;
        }
        
        return false;
    }

    /**
     * 完成会诊
     *
     * @param id 会诊申请ID
     * @param consultationRequest 会诊结果
     * @return 结果
     */
    @Override
    @Transactional
    public boolean completeConsultation(Long id, ConsultationRequest consultationRequest) {
        ConsultationRequest consultation = consultationRequestMapper.selectConsultationRequestById(id);
        if (consultation == null || !consultation.canComplete()) {
            return false;
        }
        
        // 更新会诊结果
        consultation.setConsultationFindings(consultationRequest.getConsultationFindings());
        consultation.setConsultationOpinion(consultationRequest.getConsultationOpinion());
        consultation.setConsultantSuggestion(consultationRequest.getConsultantSuggestion());
        consultation.setStatus("COMPLETED");
        consultation.setCompletionTime(new Date());
        consultation.setUpdateTime(DateUtils.getNowDate());
        consultation.setUpdateBy(SecurityUtils.getUsername());
        
        int rows = consultationRequestMapper.updateConsultationRequest(consultation);
        if (rows > 0) {
            // 异步记录审计日志
            asyncService.logCompleteConsultationAsync(consultation);

            // 异步发送通知
            asyncService.sendConsultationCompletedNotificationAsync(consultation);

            return true;
        }
        
        return false;
    }

    /**
     * 取消会诊申请
     *
     * @param id 会诊申请ID
     * @param cancelReason 取消原因
     * @return 结果
     */
    @Override
    @Transactional
    public boolean cancelConsultation(Long id, String cancelReason) {
        ConsultationRequest consultation = consultationRequestMapper.selectConsultationRequestById(id);
        if (consultation == null || !consultation.canCancel()) {
            return false;
        }
        
        // 更新状态
        consultation.setStatus("CANCELLED");
        consultation.setUpdateTime(DateUtils.getNowDate());
        consultation.setUpdateBy(SecurityUtils.getUsername());
        
        int rows = consultationRequestMapper.updateConsultationRequest(consultation);
        if (rows > 0) {
            // 异步记录审计日志
            asyncService.logCancelConsultationAsync(consultation, cancelReason);
            return true;
        }
        
        return false;
    }

    /**
     * 查询我的申请列表
     *
     * @param userId 用户ID
     * @return 会诊申请集合
     */
    @Override
    public List<ConsultationRequest> getMyRequests(Long userId) {
        List<ConsultationRequest> list = consultationRequestMapper.selectMyRequests(userId);
        for (ConsultationRequest consultation : list) {
            loadRelatedData(consultation);
        }
        return list;
    }

    /**
     * 查询我的会诊列表
     *
     * @param userId 用户ID
     * @return 会诊申请集合
     */
    @Override
    public List<ConsultationRequest> getMyConsultations(Long userId) {
        List<ConsultationRequest> list = consultationRequestMapper.selectMyConsultations(userId);
        for (ConsultationRequest consultation : list) {
            loadRelatedData(consultation);
        }
        return list;
    }

    /**
     * 查询待处理的会诊申请
     *
     * @param consultantId 会诊医生ID
     * @return 会诊申请集合
     */
    @Override
    public List<ConsultationRequest> getPendingConsultations(Long consultantId) {
        List<ConsultationRequest> list = consultationRequestMapper.selectPendingConsultations(consultantId);
        for (ConsultationRequest consultation : list) {
            loadRelatedData(consultation);
        }
        return list;
    }

    /**
     * 获取会诊统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getConsultationStatistics(Long userId) {
        return consultationRequestMapper.selectConsultationStatistics(userId);
    }

    /**
     * 生成申请编号
     *
     * @return 申请编号
     */
    @Override
    public String generateRequestNo() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        
        // 生成格式：HZ + 日期 + 4位序号
        String prefix = "HZ" + dateStr;
        
        // 查询当天已有的申请数量
        int count = 1;
        String requestNo;
        do {
            requestNo = prefix + String.format("%04d", count);
            count++;
        } while (consultationRequestMapper.checkRequestNoExists(requestNo) > 0);
        
        return requestNo;
    }

    /**
     * 处理过期的会诊申请
     *
     * @return 处理数量
     */
    @Override
    @Transactional
    public int handleExpiredConsultations() {
        String expireDaysStr = configService.selectConfigByKey("consultation.request.maxPendingDays");
        int expireDays = StringUtils.hasText(expireDaysStr) ? Integer.parseInt(expireDaysStr) : 7;
        
        String autoExpireEnabledStr = configService.selectConfigByKey("consultation.request.autoExpireEnabled");
        boolean autoExpireEnabled = "true".equals(autoExpireEnabledStr);
        
        if (!autoExpireEnabled) {
            return 0;
        }
        
        List<ConsultationRequest> expiredConsultations = consultationRequestMapper.selectExpiredConsultations(expireDays);
        int count = 0;
        
        for (ConsultationRequest consultation : expiredConsultations) {
            consultation.setStatus("CANCELLED");
            consultation.setUpdateTime(DateUtils.getNowDate());
            consultation.setUpdateBy("系统");
            
            if (consultationRequestMapper.updateConsultationRequest(consultation) > 0) {
                // 记录系统操作日志
                auditService.logSystemOperation(consultation.getId(), "EXPIRE", 
                    consultation.getStatus(), "CANCELLED", 
                    "系统自动取消过期会诊申请：" + consultation.getRequestNo(), null);
                count++;
            }
        }
        
        log.info("处理过期会诊申请完成，处理数量: {}", count);
        return count;
    }

    /**
     * 发送会诊提醒
     *
     * @return 提醒数量
     */
    @Override
    public int sendConsultationReminders() {
        String reminderEnabledStr = configService.selectConfigByKey("consultation.reminder.enabled");
        boolean reminderEnabled = "true".equals(reminderEnabledStr);
        
        if (!reminderEnabled) {
            return 0;
        }
        
        // 提醒时间点：24小时和48小时
        List<Integer> reminderHours = List.of(24, 48);
        List<ConsultationRequest> consultationsForReminder = 
            consultationRequestMapper.selectConsultationsForReminder(reminderHours);
        
        int count = 0;
        for (ConsultationRequest consultation : consultationsForReminder) {
            try {
                // 这里可以发送提醒通知
                // notificationSendService.sendSystemNotification(consultation.getConsultantId(),
                //     "会诊提醒", "您有待处理的会诊申请", "/consultation/detail/" + consultation.getRequestNo());
                count++;
            } catch (Exception e) {
                log.error("发送会诊提醒失败，申请编号: {}", consultation.getRequestNo(), e);
            }
        }
        
        log.info("发送会诊提醒完成，提醒数量: {}", count);
        return count;
    }

    /**
     * 加载关联数据
     */
    private void loadRelatedData(ConsultationRequest consultation) {
        // 加载患者检查信息
        if (consultation.getCheckId() != null) {
            PacsPatientStudy patientStudy = pacsPatientStudyMapper.selectPacsPatientStudyById(consultation.getCheckId());
            consultation.setPatientStudy(patientStudy);
        }
        
        // 加载申请医生信息
        if (consultation.getRequesterId() != null) {
            var requester = userService.selectUserById(consultation.getRequesterId());
            consultation.setRequester(requester);
        }
        
        // 加载会诊医生信息
        if (consultation.getConsultantId() != null) {
            var consultant = userService.selectUserById(consultation.getConsultantId());
            consultation.setConsultant(consultant);
        }
    }

    /**
     * 设置医院信息
     */
    private void setHospitalInfo(ConsultationRequest consultationRequest) {
        // 如果没有医院信息，从关联的检查记录中获取
        if ((consultationRequest.getHospitalId() == null || consultationRequest.getHospitalId().isEmpty()) &&
            consultationRequest.getCheckId() != null) {
            PacsPatientStudy study = pacsPatientStudyMapper.selectPacsPatientStudyById(consultationRequest.getCheckId());
            if (study != null) {
                consultationRequest.setHospitalId(study.getHospitalId());
                consultationRequest.setHospitalName(study.getHospitalName());
            }
        }
    }
}

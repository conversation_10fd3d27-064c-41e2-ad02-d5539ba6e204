package com.ruoyi.web.controller.consultation;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.consultation.domain.ConsultationNotification;
import com.ruoyi.consultation.service.IConsultationNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 会诊通知控制器
 * 提供基于SSE的实时通知功能
 */
@RestController
@RequestMapping("/consultation/notification")
@Slf4j
public class ConsultationNotificationController extends BaseController {

    @Autowired
    private IConsultationNotificationService consultationNotificationService;

    /**
     * 存储用户的SSE连接
     * Key: 用户ID, Value: SseEmitter
     */
    private static final ConcurrentHashMap<Long, SseEmitter> USER_CONNECTIONS = new ConcurrentHashMap<>();

    /**
     * 建立SSE连接
     * 用户登录后调用此接口建立长连接，接收实时通知
     */
    @GetMapping(value = "/connect", produces = "text/event-stream;charset=UTF-8")
    public SseEmitter connect(HttpServletResponse response) {
        // 设置SSE响应头
        response.setContentType("text/event-stream;charset=UTF-8");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Credentials", "true");

        // 从SecurityContext获取当前用户ID（此时已经通过认证）
        Long userId = SecurityUtils.getUserId();
        log.info("用户 {} 建立会诊通知SSE连接", userId);

        final Long finalUserId = userId; // 为lambda表达式创建final变量

        // 创建SSE发射器，设置超时时间为30分钟
        SseEmitter sseEmitter = new SseEmitter(TimeUnit.MINUTES.toMillis(30));

        // 设置连接完成回调
        sseEmitter.onCompletion(() -> {
            USER_CONNECTIONS.remove(finalUserId);
            log.info("用户 {} 的会诊通知SSE连接已完成", finalUserId);
        });

        // 设置连接超时回调
        sseEmitter.onTimeout(() -> {
            USER_CONNECTIONS.remove(finalUserId);
            log.info("用户 {} 的会诊通知SSE连接超时", finalUserId);
        });

        // 设置连接错误回调
        sseEmitter.onError((throwable) -> {
            USER_CONNECTIONS.remove(finalUserId);
            log.error("用户 {} 的会诊通知SSE连接发生错误", finalUserId, throwable);
        });

        // 存储连接
        USER_CONNECTIONS.put(finalUserId, sseEmitter);

        try {
            // 发送连接成功消息
            sseEmitter.send(SseEmitter.event()
                .name("connected")
                .data("会诊通知连接已建立"));
        } catch (IOException e) {
            log.error("发送连接成功消息失败", e);
            USER_CONNECTIONS.remove(userId);
            sseEmitter.completeWithError(e);
        }

        return sseEmitter;
    }

    /**
     * 发送会诊申请通知
     * 当有新的会诊申请时，向指定用户发送通知
     */
    @PostMapping("/send/{userId}")
    @Log(title = "发送会诊通知", businessType = BusinessType.OTHER)
    public AjaxResult sendNotification(@PathVariable Long userId, @RequestBody ConsultationNotificationMessage message) {
        try {
            boolean success = sendNotificationToUser(userId, message);
            return success ? success("通知发送成功") : error("通知发送失败");
        } catch (Exception e) {
            log.error("发送会诊通知失败", e);
            return error("发送通知异常：" + e.getMessage());
        }
    }

    /**
     * 获取在线用户数量
     */
    @GetMapping("/online-count")
    public AjaxResult getOnlineCount() {
        return success(USER_CONNECTIONS.size());
    }

    /**
     * 获取未读通知数量
     */
    @GetMapping("/unread-count")
    public AjaxResult getUnreadNotificationCount() {
        Long userId = SecurityUtils.getUserId();
        // 这里可以根据实际需求查询数据库中的未读通知数量
        // 暂时返回模拟数据
        int unreadCount = 2;
        return success(unreadCount);
    }

    /**
     * 查询通知列表
     */
    @GetMapping("/list")
    public TableDataInfo getNotificationList(ConsultationNotification consultationNotification) {
        startPage();
        Long userId = SecurityUtils.getUserId();
        // 设置当前用户ID作为查询条件
        consultationNotification.setRecipientId(userId);
        List<ConsultationNotification> list = consultationNotificationService.selectConsultationNotificationList(consultationNotification);
        return getDataTable(list);
    }

    /**
     * 查询通知历史记录
     */
    @GetMapping("/history")
    @PreAuthorize("@ss.hasPermi('consultation:notification:list')")
    public TableDataInfo getNotificationHistory(ConsultationNotification consultationNotification) {
        startPage();
        List<ConsultationNotification> list = consultationNotificationService.selectConsultationNotificationList(consultationNotification);
        return getDataTable(list);
    }

    /**
     * 获取通知详情
     */
    @GetMapping("/history/{id}")
    @PreAuthorize("@ss.hasPermi('consultation:notification:query')")
    public AjaxResult getNotificationDetail(@PathVariable("id") Long id) {
        return success(consultationNotificationService.selectConsultationNotificationById(id));
    }

    /**
     * 重试失败的通知
     */
    @PostMapping("/retry-failed")
    @PreAuthorize("@ss.hasPermi('consultation:notification:retry')")
    public AjaxResult retryFailedNotifications() {
        int count = consultationNotificationService.retryFailedNotifications();
        return success("已提交重试任务，处理数量：" + count);
    }

    /**
     * 测试发送通知给指定用户
     */
    @PostMapping("/test-send/{userId}")
    @PreAuthorize("@ss.hasPermi('consultation:notification:send')")
    public AjaxResult testSendNotification(@PathVariable("userId") Long userId) {
        try {
            ConsultationNotificationMessage message = new ConsultationNotificationMessage();
            message.setType("REQUEST");
            message.setTitle("测试会诊通知");
            message.setContent("这是一条测试通知，用于验证SSE连接是否正常");
            message.setPatientName("测试患者");
            message.setRequesterName("测试医生");
            message.setUrgencyLevel("NORMAL");
            message.setRequestNo("TEST-" + System.currentTimeMillis());
            message.setTimestamp(System.currentTimeMillis());

            boolean sent = sendNotificationToUser(userId, message);

            if (sent) {
                return success("测试通知发送成功");
            } else {
                return error("测试通知发送失败：用户可能不在线");
            }
        } catch (Exception e) {
            log.error("发送测试通知异常", e);
            return error("发送测试通知异常：" + e.getMessage());
        }
    }

    /**
     * 向指定用户发送通知
     */
    public static boolean sendNotificationToUser(Long userId, ConsultationNotificationMessage message) {
        SseEmitter sseEmitter = USER_CONNECTIONS.get(userId);
        if (sseEmitter == null) {
            log.warn("用户 {} 未建立SSE连接，无法发送通知", userId);
            return false;
        }

        try {
            sseEmitter.send(SseEmitter.event()
                .name("consultation-notification")
                .data(message));
            log.info("成功向用户 {} 发送会诊通知", userId);
            return true;
        } catch (IOException e) {
            log.error("向用户 {} 发送会诊通知失败", userId, e);
            // 移除失效的连接
            USER_CONNECTIONS.remove(userId);
            return false;
        }
    }

    /**
     * 广播通知给所有在线用户
     */
    public static void broadcastNotification(ConsultationNotificationMessage message) {
        USER_CONNECTIONS.forEach((userId, sseEmitter) -> {
            try {
                sseEmitter.send(SseEmitter.event()
                    .name("consultation-broadcast")
                    .data(message));
            } catch (IOException e) {
                log.error("向用户 {} 广播会诊通知失败", userId, e);
                USER_CONNECTIONS.remove(userId);
            }
        });
    }

    /**
     * 会诊通知消息类
     */
    public static class ConsultationNotificationMessage {
        private String type;           // 通知类型：REQUEST, ACCEPT, REJECT, COMPLETE
        private String title;          // 通知标题
        private String content;        // 通知内容
        private String requestNo;      // 申请编号
        private Long consultationId;   // 会诊申请ID
        private String requesterName;  // 申请人姓名
        private String patientName;    // 患者姓名
        private String urgencyLevel;   // 紧急程度
        private Long timestamp;        // 时间戳

        // 构造函数
        public ConsultationNotificationMessage() {
            this.timestamp = System.currentTimeMillis();
        }

        public ConsultationNotificationMessage(String type, String title, String content) {
            this();
            this.type = type;
            this.title = title;
            this.content = content;
        }

        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        public String getRequestNo() { return requestNo; }
        public void setRequestNo(String requestNo) { this.requestNo = requestNo; }
        
        public Long getConsultationId() { return consultationId; }
        public void setConsultationId(Long consultationId) { this.consultationId = consultationId; }
        
        public String getRequesterName() { return requesterName; }
        public void setRequesterName(String requesterName) { this.requesterName = requesterName; }
        
        public String getPatientName() { return patientName; }
        public void setPatientName(String patientName) { this.patientName = patientName; }
        
        public String getUrgencyLevel() { return urgencyLevel; }
        public void setUrgencyLevel(String urgencyLevel) { this.urgencyLevel = urgencyLevel; }
        
        public Long getTimestamp() { return timestamp; }
        public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }
    }
}

package com.ruoyi.web.controller.consultation;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.consultation.domain.PersistentNotification;
import com.ruoyi.consultation.message.ConsultationNotificationMessage;
import com.ruoyi.consultation.service.ReliableNotificationService;
import com.ruoyi.consultation.service.SseConnectionService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;

/**
 * 可靠通知控制器
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RestController
@RequestMapping("/consultation/reliable-notification")
@Slf4j
public class ReliableNotificationController extends BaseController {

    @Autowired
    private ReliableNotificationService reliableNotificationService;

    @Autowired
    private SseConnectionService sseConnectionService;

    /**
     * 建立SSE连接
     */
    @GetMapping(value = "/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter connect(HttpServletRequest request, HttpServletResponse response) {
        // 设置响应头
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Credentials", "true");

        Long userId = SecurityUtils.getUserId();
        log.info("用户 {} 请求建立SSE连接", userId);

        // 通过Service创建连接
        SseEmitter emitter = sseConnectionService.createConnection(userId);

        // 处理用户上线
        reliableNotificationService.handleUserOnline(userId);

        return emitter;
    }

    /**
     * 确认通知已收到
     */
    @PostMapping("/acknowledge/{notificationId}")
    @Log(title = "确认通知", businessType = BusinessType.UPDATE)
    public AjaxResult acknowledgeNotification(@PathVariable Long notificationId) {
        Long userId = SecurityUtils.getUserId();
        boolean success = reliableNotificationService.acknowledgeNotification(notificationId, userId);
        
        if (success) {
            return success();
        } else {
            return error("确认通知失败");
        }
    }

    /**
     * 获取离线通知
     */
    @GetMapping("/offline")
    public AjaxResult getOfflineNotifications() {
        Long userId = SecurityUtils.getUserId();
        // 这个方法会在用户上线时自动调用，这里提供手动获取接口
        return success("离线通知将在连接建立时自动推送");
    }

    /**
     * 获取通知列表
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermi('consultation:notification:list')")
    public TableDataInfo list(PersistentNotification notification) {
        startPage();
        Long userId = SecurityUtils.getUserId();
        notification.setUserId(userId);
        List<PersistentNotification> list = reliableNotificationService.getNotificationList(notification);
        return getDataTable(list);
    }

    /**
     * 标记通知为已读
     */
    @PostMapping("/mark-read/{notificationId}")
    @Log(title = "标记通知已读", businessType = BusinessType.UPDATE)
    public AjaxResult markAsRead(@PathVariable Long notificationId) {
        Long userId = SecurityUtils.getUserId();
        boolean success = reliableNotificationService.acknowledgeNotification(notificationId, userId);
        return success ? success() : error("标记失败");
    }

    /**
     * 批量标记通知为已读
     */
    @PostMapping("/batch-mark-read")
    @Log(title = "批量标记通知已读", businessType = BusinessType.UPDATE)
    public AjaxResult batchMarkAsRead(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> notificationIds = (List<Long>) params.get("notificationIds");
        Long userId = SecurityUtils.getUserId();
        
        int successCount = 0;
        for (Long notificationId : notificationIds) {
            if (reliableNotificationService.acknowledgeNotification(notificationId, userId)) {
                successCount++;
            }
        }
        
        return success("成功标记 " + successCount + " 条通知为已读");
    }

    /**
     * 获取通知统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> statistics = reliableNotificationService.getNotificationStatistics(userId);
        return success(statistics);
    }

    /**
     * 检查用户在线状态
     */
    @GetMapping("/online-status/{userId}")
    @PreAuthorize("@ss.hasPermi('consultation:notification:query')")
    public AjaxResult checkOnlineStatus(@PathVariable Long userId) {
        boolean isOnline = sseConnectionService.isUserOnline(userId);
        return success(Map.of("userId", userId, "isOnline", isOnline));
    }

    /**
     * 测试通知发送
     */
    @PostMapping("/test")
    @PreAuthorize("@ss.hasPermi('consultation:notification:test')")
    @Log(title = "测试通知发送", businessType = BusinessType.OTHER)
    public AjaxResult testNotification(@RequestBody Map<String, Object> testData) {
        try {
            Long userId = SecurityUtils.getUserId();
            String title = (String) testData.getOrDefault("title", "测试通知");
            String content = (String) testData.getOrDefault("content", "这是一条测试通知");
            
            // 创建测试通知消息
            ConsultationNotificationMessage message = new ConsultationNotificationMessage();
            message.setType("TEST");
            message.setTitle(title);
            message.setContent(content);
            message.setUrl("/consultation/test");
            
            Long notificationId = reliableNotificationService.sendReliableNotification(userId, message, null);
            
            if (notificationId != null) {
                return success("测试通知发送成功，通知ID: " + notificationId);
            } else {
                return error("测试通知发送失败");
            }
            
        } catch (Exception e) {
            log.error("测试通知发送异常", e);
            return error("测试通知发送异常: " + e.getMessage());
        }
    }

    /**
     * 手动触发离线通知处理
     */
    @PostMapping("/process-offline")
    @PreAuthorize("@ss.hasPermi('consultation:notification:manage')")
    @Log(title = "处理离线通知", businessType = BusinessType.OTHER)
    public AjaxResult processOfflineNotifications() {
        Long userId = SecurityUtils.getUserId();
        reliableNotificationService.handleUserOnline(userId);
        return success("离线通知处理已触发");
    }

    /**
     * 清理过期通知
     */
    @PostMapping("/cleanup")
    @PreAuthorize("@ss.hasPermi('consultation:notification:manage')")
    @Log(title = "清理过期通知", businessType = BusinessType.DELETE)
    public AjaxResult cleanupExpiredNotifications(@RequestBody Map<String, Object> params) {
        Integer retentionDays = (Integer) params.getOrDefault("retentionDays", 30);
        reliableNotificationService.cleanupExpiredNotifications(retentionDays);
        return success("过期通知清理任务已启动");
    }



    /**
     * 获取在线用户数量
     */
    @GetMapping("/online-count")
    @PreAuthorize("@ss.hasPermi('consultation:notification:query')")
    public AjaxResult getOnlineCount() {
        return success(Map.of("onlineCount", sseConnectionService.getOnlineUserCount()));
    }

    /**
     * 获取在线用户列表
     */
    @GetMapping("/online-users")
    @PreAuthorize("@ss.hasPermi('consultation:notification:query')")
    public AjaxResult getOnlineUsers() {
        return success(Map.of("onlineUsers", sseConnectionService.getOnlineUserIds()));
    }

    /**
     * 获取连接统计信息
     */
    @GetMapping("/connection-stats")
    @PreAuthorize("@ss.hasPermi('consultation:notification:query')")
    public AjaxResult getConnectionStatistics() {
        return success(sseConnectionService.getConnectionStatistics());
    }

    /**
     * 广播通知给所有在线用户
     */
    @PostMapping("/broadcast")
    @PreAuthorize("@ss.hasPermi('consultation:notification:broadcast')")
    @Log(title = "广播通知", businessType = BusinessType.OTHER)
    public AjaxResult broadcastNotification(@RequestBody Map<String, Object> params) {
        String title = (String) params.get("title");
        String content = (String) params.get("content");

        ConsultationNotificationMessage message = ConsultationNotificationMessage
            .createSystemNotification(title, content, null);

        int successCount = sseConnectionService.broadcastNotification(message);

        return success("广播通知完成，成功发送给 " + successCount + " 个用户");
    }
}

-- =====================================================
-- 持久化通知表 - MySQL 兼容版
-- 用于确保会诊通知100%送达
-- =====================================================


-- =====================================================
-- 0. 准备工作：创建 sys_config 表 (如果不存在)
-- 你的原始脚本中引用了此表，但未提供创建语句。
-- =====================================================
CREATE TABLE IF NOT EXISTS sys_config (
                                          config_id     INT(5)        NOT NULL PRIMARY KEY AUTO_INCREMENT,
                                          config_name   VARCHAR(100)  DEFAULT '' COMMENT '参数名称',
                                          config_key    VARCHAR(100)  DEFAULT '' COMMENT '参数键名',
                                          config_value  VARCHAR(500)  DEFAULT '' COMMENT '参数键值',
                                          config_type   CHAR(1)       DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
                                          create_by     VARCHAR(64)   DEFAULT '' COMMENT '创建者',
                                          create_time   DATETIME      COMMENT '创建时间',
                                          update_by     VARCHAR(64)   DEFAULT '' COMMENT '更新者',
                                          update_time   DATETIME      COMMENT '更新时间',
                                          remark        VARCHAR(500)  DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='参数配置表';


-- =====================================================
-- 1. 创建持久化通知表
-- =====================================================

CREATE TABLE IF NOT EXISTS persistent_notification (
                                                       id BIGINT PRIMARY KEY AUTO_INCREMENT,                     -- MySQL 使用 AUTO_INCREMENT 替代 IDENTITY
                                                       user_id BIGINT NOT NULL,                                  -- 用户ID
                                                       consultation_id BIGINT,                                   -- 会诊申请ID（可选）
                                                       notification_type VARCHAR(50) NOT NULL,                   -- 通知类型
                                                       title VARCHAR(200) NOT NULL,                              -- 通知标题
                                                       content TEXT,                                             -- 通知内容
                                                       message_data JSON,                                        -- 消息数据 (MySQL 5.7+ 支持 JSON 类型，更高效)

    -- 状态管理
                                                       status VARCHAR(20) DEFAULT 'PENDING',                     -- 状态：PENDING-待发送，SENT-已发送，DELIVERED-已送达，FAILED-失败
                                                       retry_count INT DEFAULT 0,                                -- 重试次数
                                                       max_retry_count INT DEFAULT 10,                           -- 最大重试次数
                                                       next_retry_time DATETIME(6),                              -- 下次重试时间 (DATETIME(6) 对应 SQL Server 的 DATETIME2)

    -- 时间信息
                                                       send_time DATETIME(6),                                    -- 发送时间
                                                       delivery_time DATETIME(6),                                -- 送达时间
                                                       ack_time DATETIME(6),                                     -- 确认时间
                                                       expire_time DATETIME(6),                                  -- 过期时间

    -- 错误信息
                                                       error_message TEXT,                                       -- 错误信息

    -- 优先级和配置
                                                       priority INT DEFAULT 2,                                   -- 优先级：1-低，2-普通，3-高，4-紧急
                                                       require_ack TINYINT DEFAULT 1,                            -- 是否需要确认：0-否，1-是

    -- 设备信息
                                                       device_type VARCHAR(20) DEFAULT 'WEB',                    -- 设备类型：WEB-网页，MOBILE-移动端
                                                       user_agent TEXT,                                          -- 用户代理信息
                                                       ip_address VARCHAR(50),                                   -- IP地址

    -- 审计字段
                                                       create_by VARCHAR(64) DEFAULT '',                         -- 创建者
                                                       create_time DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6),     -- MySQL 使用 CURRENT_TIMESTAMP 替代 GETDATE()
                                                       update_by VARCHAR(64) DEFAULT '',                         -- 更新者
                                                       update_time DATETIME(6) ON UPDATE CURRENT_TIMESTAMP(6),   -- 更新时间 (使用 ON UPDATE 子句自动更新)
                                                       remark VARCHAR(500)                                       -- 备注
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='持久化通知表';


-- =====================================================
-- 2. 创建索引 (语法与 SQL Server 基本相同)
-- =====================================================

-- 用户ID索引（用于查询用户的通知）
CREATE INDEX idx_persistent_notification_user_id
    ON persistent_notification(user_id);

-- 状态索引（用于查询待处理的通知）
CREATE INDEX idx_persistent_notification_status
    ON persistent_notification(status);

-- 会诊ID索引（用于查询特定会诊的通知）
CREATE INDEX idx_persistent_notification_consultation_id
    ON persistent_notification(consultation_id);

-- 用户状态复合索引（用于查询用户的特定状态通知）
CREATE INDEX idx_persistent_notification_user_status
    ON persistent_notification(user_id, status);

-- 重试时间索引（用于定时任务查询需要重试的通知）
CREATE INDEX idx_persistent_notification_retry_time
    ON persistent_notification(next_retry_time, status);

-- 创建时间索引（用于清理过期数据）
CREATE INDEX idx_persistent_notification_create_time
    ON persistent_notification(create_time);

-- 优先级和状态复合索引（用于按优先级处理通知）
CREATE INDEX idx_persistent_notification_priority_status
    ON persistent_notification(priority DESC, status, create_time);


-- =====================================================
-- 3. 添加约束 (MySQL 8.0.16+ 才真正强制执行 CHECK 约束)
-- =====================================================

-- 添加外键约束（如果需要，请取消注释并确保关联表存在）
-- ALTER TABLE persistent_notification
-- ADD CONSTRAINT fk_persistent_notification_user
-- FOREIGN KEY (user_id) REFERENCES sys_user(user_id);

-- ALTER TABLE persistent_notification
-- ADD CONSTRAINT fk_persistent_notification_consultation
-- FOREIGN KEY (consultation_id) REFERENCES consultation_request(id);

-- 添加检查约束
ALTER TABLE persistent_notification
    ADD CONSTRAINT chk_persistent_notification_status
        CHECK (status IN ('PENDING', 'SENT', 'DELIVERED', 'FAILED'));

ALTER TABLE persistent_notification
    ADD CONSTRAINT chk_persistent_notification_priority
        CHECK (priority BETWEEN 1 AND 4);

ALTER TABLE persistent_notification
    ADD CONSTRAINT chk_persistent_notification_require_ack
        CHECK (require_ack IN (0, 1));


-- =====================================================
-- 4. 创建通知统计视图
-- =====================================================

CREATE OR REPLACE VIEW v_notification_statistics AS
SELECT
    user_id,
    COUNT(*) as total_notifications,
    SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END) as pending_count,    -- 使用 SUM 更常见
    SUM(CASE WHEN status = 'SENT' THEN 1 ELSE 0 END) as sent_count,
    SUM(CASE WHEN status = 'DELIVERED' THEN 1 ELSE 0 END) as delivered_count,
    SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_count,
    AVG(retry_count) as avg_retry_count,
    MAX(create_time) as last_notification_time,
    -- MySQL 中整数相除会得到浮点数，但显式转换更安全
    (SUM(CASE WHEN status = 'DELIVERED' THEN 1 ELSE 0 END) / COUNT(*)) * 100 as delivery_rate_percentage
FROM persistent_notification
GROUP BY user_id;


-- =====================================================
-- 5. 创建通知队列表（Redis备份）
-- =====================================================

CREATE TABLE IF NOT EXISTS notification_queue (
                                                  id BIGINT PRIMARY KEY AUTO_INCREMENT,
                                                  user_id BIGINT NOT NULL,                              -- 用户ID
                                                  notification_id BIGINT NOT NULL,                      -- 通知ID
                                                  queue_type VARCHAR(20) DEFAULT 'OFFLINE',             -- 队列类型：OFFLINE-离线队列，RETRY-重试队列
                                                  priority INT DEFAULT 2,                               -- 优先级
                                                  create_time DATETIME(6) DEFAULT CURRENT_TIMESTAMP(6), -- 创建时间
                                                  process_time DATETIME(6),                             -- 处理时间
                                                  status VARCHAR(20) DEFAULT 'PENDING'                  -- 状态：PENDING-待处理，PROCESSED-已处理
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知队列备份表';

-- 队列索引
CREATE INDEX idx_notification_queue_user_id
    ON notification_queue(user_id, status);

CREATE INDEX idx_notification_queue_priority
    ON notification_queue(priority DESC, create_time);


-- =====================================================
-- 6. 插入初始配置数据
-- =====================================================

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) VALUES
                                                                                                                ('通知最大重试次数', 'notification.max.retry.count', '10', 'Y', 'admin', NOW(), '通知发送失败时的最大重试次数'),
                                                                                                                ('通知确认超时时间', 'notification.ack.timeout.seconds', '30', 'Y', 'admin', NOW(), '通知确认超时时间（秒）'),
                                                                                                                ('离线通知保留天数', 'notification.offline.retention.days', '7', 'Y', 'admin', NOW(), '离线通知在队列中的保留天数'),
                                                                                                                ('通知清理保留天数', 'notification.cleanup.retention.days', '30', 'Y', 'admin', NOW(), '已送达通知的保留天数'),
                                                                                                                ('通知重试间隔基数', 'notification.retry.interval.base.seconds', '60', 'Y', 'admin', NOW(), '通知重试间隔基数（秒），实际间隔为基数*重试次数'),
                                                                                                                ('启用通知持久化', 'notification.persistence.enabled', 'true', 'Y', 'admin', NOW(), '是否启用通知持久化功能');


-- =====================================================
-- 7. 创建存储过程 (MySQL 语法)
-- =====================================================

-- 修改分隔符，以便在存储过程中使用分号
DELIMITER //

CREATE PROCEDURE sp_cleanup_expired_notifications(
    IN p_retention_days INT
)
BEGIN
    DECLARE v_cutoff_date DATETIME(6);
    DECLARE v_deleted_count_1 INT DEFAULT 0;
    DECLARE v_deleted_count_2 INT DEFAULT 0;
    DECLARE v_deleted_count_3 INT DEFAULT 0;

    -- 计算截止日期
    SET v_cutoff_date = DATE_SUB(NOW(), INTERVAL p_retention_days DAY);

    -- 删除过期的已送达通知
    DELETE FROM persistent_notification
    WHERE status = 'DELIVERED'
      AND delivery_time < v_cutoff_date;
    SET v_deleted_count_1 = ROW_COUNT(); -- MySQL 使用 ROW_COUNT() 获取影响行数

    -- 删除过期的失败通知
    DELETE FROM persistent_notification
    WHERE status = 'FAILED'
      AND create_time < v_cutoff_date;
    SET v_deleted_count_2 = ROW_COUNT();

    -- 清理队列表
    DELETE FROM notification_queue
    WHERE status = 'PROCESSED'
      AND process_time < v_cutoff_date;
    SET v_deleted_count_3 = ROW_COUNT();

    -- 返回清理数量
    SELECT (v_deleted_count_1 + v_deleted_count_2 + v_deleted_count_3) as total_deleted_count;
END //

-- 恢复默认分隔符
DELIMITER ;


-- =====================================================
-- 8. 创建触发器 (MySQL 语法)
-- 注意：在创建表时已使用 ON UPDATE CURRENT_TIMESTAMP(6)，此触发器不再是必需的。
-- 但作为语法示例保留。
-- =====================================================
/*
-- 如果不使用 ON UPDATE 子句，可以使用这个触发器
DELIMITER //
CREATE TRIGGER trg_persistent_notification_update
BEFORE UPDATE ON persistent_notification
FOR EACH ROW
BEGIN
    -- 'inserted' 是 SQL Server 的概念，MySQL 中使用 NEW
    SET NEW.update_time = NOW(6);
END //
DELIMITER ;
*/

-- =====================================================
-- 9. 验证表创建 (MySQL 语法)
-- =====================================================

-- 检查表是否创建成功
SELECT 'persistent_notification' AS table_name, 'Table structure:' AS details;
DESCRIBE persistent_notification;

SELECT 'notification_queue' AS table_name, 'Table structure:' AS details;
DESCRIBE notification_queue;

-- 检查索引是否创建成功
SELECT 'persistent_notification' AS table_name, 'Indexes:' AS details;
SHOW INDEX FROM persistent_notification;

-- 输出创建完成标识
SELECT 'persistent_notification_schema_created_successfully' AS status, NOW() AS completion_time;

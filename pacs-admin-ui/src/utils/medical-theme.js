/**
 * 医疗主题工具类
 * 提供医疗场景相关的样式类名和工具方法
 */

// 医疗主题色彩常量
export const MEDICAL_COLORS = {
  PRIMARY: '#0066cc',
  PRIMARY_LIGHT: '#4d94ff',
  PRIMARY_DARK: '#004499',
  SUCCESS: '#2e7d32',
  WARNING: '#fa8c16',
  DANGER: '#ff4d4f',
  INFO: '#8c8c8c'
}

// 诊断状态样式映射
export const DIAGNOSIS_STATUS_STYLES = {
  '-1': { // 待诊断
    type: 'warning',
    class: 'medical-status-tag pending',
    color: MEDICAL_COLORS.WARNING,
    text: '待诊断'
  },
  '1': { // 已诊断
    type: 'success',
    class: 'medical-status-tag diagnosed',
    color: MEDICAL_COLORS.SUCCESS,
    text: '已诊断'
  },
  '2': { // 已审核
    type: 'primary',
    class: 'medical-status-tag audited',
    color: MEDICAL_COLORS.PRIMARY,
    text: '已审核'
  },
  '9': { // 院内诊断
    type: 'info',
    class: 'medical-status-tag archived',
    color: '#722ed1',
    text: '院内诊断'
  }
}

// DICOM同步状态样式映射
export const DICOM_SYNC_STATUS_STYLES = {
  '0': { // 待同步
    type: 'warning',
    class: 'medical-status-tag sync-pending',
    color: MEDICAL_COLORS.WARNING,
    text: '待同步'
  },
  '1': { // 已同步
    type: 'success',
    class: 'medical-status-tag synced',
    color: MEDICAL_COLORS.SUCCESS,
    text: '已同步'
  }
}

// 医疗主题CSS类名
export const MEDICAL_CLASSES = {
  // 表格相关
  TABLE: 'medical-table',
  TABLE_HEADER: 'medical-table-header',
  TABLE_ROW: 'medical-table-row',
  
  // 卡片相关
  CARD: 'medical-card',
  STATS_CARD: 'medical-stats-card',
  
  // 按钮相关
  BUTTON: 'medical-button',
  BUTTON_PRIMARY: 'medical-button el-button--primary',
  BUTTON_SUCCESS: 'medical-button el-button--success',
  BUTTON_WARNING: 'medical-button el-button--warning',
  BUTTON_DANGER: 'medical-button el-button--danger',
  
  // 表单相关
  FORM: 'medical-form',
  
  // 分页相关
  PAGINATION: 'medical-pagination',
  
  // 状态标签
  STATUS_TAG: 'medical-status-tag',
  
  // 提示相关
  ALERT: 'medical-alert',
  ALERT_CRITICAL: 'medical-alert medical-alert--critical',
  ALERT_WARNING: 'medical-alert medical-alert--warning',
  ALERT_INFO: 'medical-alert medical-alert--info',
  ALERT_SUCCESS: 'medical-alert medical-alert--success'
}

/**
 * 获取诊断状态样式
 * @param {string|number} status 诊断状态
 * @returns {object} 状态样式对象
 */
export function getDiagnosisStatusStyle(status) {
  const statusStr = String(status)
  return DIAGNOSIS_STATUS_STYLES[statusStr] || {
    type: 'info',
    class: 'medical-status-tag',
    color: MEDICAL_COLORS.INFO,
    text: '未知状态'
  }
}

/**
 * 获取DICOM同步状态样式
 * @param {string|number} status DICOM同步状态
 * @returns {object} 状态样式对象
 */
export function getDicomSyncStatusStyle(status) {
  const statusStr = String(status)
  return DICOM_SYNC_STATUS_STYLES[statusStr] || {
    type: 'info',
    class: 'medical-status-tag',
    color: MEDICAL_COLORS.INFO,
    text: '未知状态'
  }
}

/**
 * 获取医疗主题消息样式
 * @param {string} type 消息类型 success|warning|error|info
 * @returns {object} 消息样式配置
 */
export function getMedicalMessageStyle(type) {
  const styles = {
    success: {
      customClass: 'medical-message-success',
      iconClass: 'el-icon-success',
      duration: 3000
    },
    warning: {
      customClass: 'medical-message-warning',
      iconClass: 'el-icon-warning',
      duration: 4000
    },
    error: {
      customClass: 'medical-message-error',
      iconClass: 'el-icon-error',
      duration: 5000
    },
    info: {
      customClass: 'medical-message-info',
      iconClass: 'el-icon-info',
      duration: 3000
    }
  }
  
  return styles[type] || styles.info
}

/**
 * 获取医疗主题通知样式
 * @param {string} type 通知类型
 * @param {string} title 通知标题
 * @returns {object} 通知样式配置
 */
export function getMedicalNotificationStyle(type, title = '') {
  const baseConfig = {
    title: title || '系统通知',
    duration: 4500,
    position: 'top-right'
  }
  
  const typeConfigs = {
    success: {
      ...baseConfig,
      customClass: 'medical-notification-success',
      iconClass: 'el-icon-success'
    },
    warning: {
      ...baseConfig,
      customClass: 'medical-notification-warning',
      iconClass: 'el-icon-warning',
      duration: 6000
    },
    error: {
      ...baseConfig,
      customClass: 'medical-notification-error',
      iconClass: 'el-icon-error',
      duration: 8000
    },
    info: {
      ...baseConfig,
      customClass: 'medical-notification-info',
      iconClass: 'el-icon-info'
    }
  }
  
  return typeConfigs[type] || typeConfigs.info
}

/**
 * 应用医疗主题到Element Plus组件
 * @param {string} componentType 组件类型
 * @returns {object} 组件样式配置
 */
export function applyMedicalTheme(componentType) {
  const themeConfigs = {
    table: {
      class: MEDICAL_CLASSES.TABLE,
      headerCellStyle: {
        background: 'var(--table-header-bg)',
        color: 'var(--table-header-text)',
        fontWeight: '600'
      },
      cellStyle: {
        borderBottom: '1px solid #f0f0f0'
      }
    },
    
    card: {
      class: MEDICAL_CLASSES.CARD,
      shadow: 'hover',
      bodyStyle: {
        padding: '20px'
      }
    },
    
    form: {
      class: MEDICAL_CLASSES.FORM,
      labelPosition: 'top',
      labelWidth: 'auto'
    },
    
    pagination: {
      class: MEDICAL_CLASSES.PAGINATION,
      background: true,
      layout: 'total, sizes, prev, pager, next, jumper'
    }
  }
  
  return themeConfigs[componentType] || {}
}

/**
 * 生成医疗主题渐变背景
 * @param {string} color 主色调
 * @param {number} angle 渐变角度，默认135度
 * @returns {string} CSS渐变背景
 */
export function generateMedicalGradient(color, angle = 135) {
  const lightColor = lightenColor(color, 0.1)
  return `linear-gradient(${angle}deg, ${color} 0%, ${lightColor} 100%)`
}

/**
 * 颜色加亮工具函数
 * @param {string} color 十六进制颜色值
 * @param {number} amount 加亮程度 0-1
 * @returns {string} 加亮后的颜色
 */
function lightenColor(color, amount) {
  const usePound = color[0] === '#'
  const col = usePound ? color.slice(1) : color
  const num = parseInt(col, 16)
  let r = (num >> 16) + amount * 255
  let g = (num >> 8 & 0x00FF) + amount * 255
  let b = (num & 0x0000FF) + amount * 255
  
  r = r > 255 ? 255 : r < 0 ? 0 : r
  g = g > 255 ? 255 : g < 0 ? 0 : g
  b = b > 255 ? 255 : b < 0 ? 0 : b
  
  return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0')
}

// 导出默认配置
export default {
  MEDICAL_COLORS,
  DIAGNOSIS_STATUS_STYLES,
  DICOM_SYNC_STATUS_STYLES,
  MEDICAL_CLASSES,
  getDiagnosisStatusStyle,
  getDicomSyncStatusStyle,
  getMedicalMessageStyle,
  getMedicalNotificationStyle,
  applyMedicalTheme,
  generateMedicalGradient
}

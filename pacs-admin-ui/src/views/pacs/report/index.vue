<template>
  <div class="app-container">
    <!-- 紧凑搜索栏 -->
    <div v-show="showSearch" class="compact-search-bar">
      <el-form :model="queryParams" ref="queryRef" :inline="true" size="small" class="compact-form">
        <el-form-item label="患者ID" prop="originalPatientId">
          <el-input
            v-model="queryParams.originalPatientId"
            placeholder="请输入患者ID"
            clearable
            style="width: 160px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="检查编码" prop="examCode">
          <el-input
            v-model="queryParams.examCode"
            placeholder="请输入检查编码"
            clearable
            style="width: 160px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="医生姓名" prop="doctorName">
          <el-input
            v-model="queryParams.doctorName"
            placeholder="请输入医生姓名"
            clearable
            style="width: 160px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button type="text" icon="More" @click="showAdvancedSearch = true">更多筛选</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 高级搜索弹窗 -->
    <el-dialog
      v-model="showAdvancedSearch"
      title="高级搜索"
      width="900px"
      :before-close="handleAdvancedClose"
    >
      <el-form :model="advancedParams" label-width="120px">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="原始患者ID">
              <el-input
                v-model="advancedParams.originalPatientId"
                placeholder="请输入原始患者ID"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查编码">
              <el-input
                v-model="advancedParams.examCode"
                placeholder="请输入检查编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="医生姓名">
              <el-input
                v-model="advancedParams.doctorName"
                placeholder="请输入医生姓名"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="报告状态">
              <el-input
                v-model="advancedParams.reportState"
                placeholder="请输入报告状态"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="原始检查编码">
              <el-input
                v-model="advancedParams.originalExamCode"
                placeholder="请输入原始检查编码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查类型">
              <el-input
                v-model="advancedParams.modality"
                placeholder="请输入检查类型"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="审核医生姓名">
              <el-input
                v-model="advancedParams.reviewDoctorName"
                placeholder="请输入审核医生姓名"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否阳性">
              <el-input
                v-model="advancedParams.positive"
                placeholder="请输入是否阳性"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否传染病">
              <el-input
                v-model="advancedParams.infectiousDisease"
                placeholder="请输入是否传染病"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="书写时间">
              <el-date-picker
                v-model="advancedParams.writeTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择书写时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="审核时间">
              <el-date-picker
                v-model="advancedParams.auditTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择审核时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="同步时间">
              <el-date-picker
                v-model="advancedParams.syncTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择同步时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleAdvancedClose">取消</el-button>
          <el-button @click="resetAdvancedSearch">重置</el-button>
          <el-button type="primary" @click="handleAdvancedSearch">搜索</el-button>
        </div>
      </template>
    </el-dialog>



    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="reportList">
      <el-table-column label="患者ID" align="center" prop="originalPatientId" min-width="120" />
      <el-table-column label="检查编码" align="center" prop="examCode" min-width="120" />
      <el-table-column label="检查类型" align="center" prop="modality" min-width="100" />
      <el-table-column label="医生姓名" align="center" prop="doctorName" min-width="100" />
      <el-table-column label="审核医生" align="center" prop="reviewDoctorName" min-width="100" />
      <el-table-column label="审核时间" align="center" prop="auditTime" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报告状态" align="center" prop="reportState" min-width="100" />
      <el-table-column label="操作" align="center" width="80" fixed="right">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看检查报告对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form :model="form" label-width="120px" class="view-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="患者ID">
              <span class="form-value">{{ form.originalPatientId || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查编码">
              <span class="form-value">{{ form.examCode || '-' }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="检查类型">
              <span class="form-value">{{ form.modality || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报告科室">
              <span class="form-value">{{ form.reportDepartment || '-' }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="医生姓名">
              <span class="form-value">{{ form.doctorName || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审核医生">
              <span class="form-value">{{ form.reviewDoctorName || '-' }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="书写时间">
              <span class="form-value">{{ parseTime(form.writeTime, '{y}-{m}-{d}') || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审核时间">
              <span class="form-value">{{ parseTime(form.auditTime, '{y}-{m}-{d}') || '-' }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告状态">
              <span class="form-value">{{ form.reportState || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最后审核时间">
              <span class="form-value">{{ parseTime(form.lastAuditTime, '{y}-{m}-{d}') || '-' }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="检查所见">
          <div class="form-textarea">{{ form.see || '-' }}</div>
        </el-form-item>

        <el-form-item label="诊断结论">
          <div class="form-textarea">{{ form.diagnosis || '-' }}</div>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否阳性">
              <span class="form-value">{{ form.positive || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否传染病">
              <span class="form-value">{{ form.infectiousDisease || '-' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Report">
import { listReport, getReport } from "@/api/pacs/report";
import { ArrowDown } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

const reportList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const showAdvancedSearch = ref(false); // 控制高级搜索弹窗
const expandSearch = ref(false);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    originalPatientId: null,
    originalExamCode: null,
    examCode: null,
    originalReportId: null,
    modality: null,
    doctorName: null,
    reviewDoctorName: null,
    writeTime: null,
    auditTime: null,
    lastAuditTime: null,
    reportState: null,
    positive: null,
    infectiousDisease: null,
    syncTime: null,
  }
});

const { queryParams, form } = toRefs(data);

// 高级搜索参数
const advancedParams = reactive({
  originalPatientId: '',
  examCode: '',
  doctorName: '',
  reportState: '',
  originalExamCode: '',
  modality: '',
  reviewDoctorName: '',
  positive: '',
  infectiousDisease: '',
  writeTime: '',
  auditTime: '',
  syncTime: ''
});

/** 查询检查报告列表 */
function getList() {
  loading.value = true;
  listReport(queryParams.value).then(response => {
    reportList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {};
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 查看按钮操作 */
function handleView(row) {
  reset();
  getReport(row.id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "查看检查报告";
  });
}

/** 切换展开收起查询条件 */
function toggleExpandSearch() {
  expandSearch.value = !expandSearch.value;
}

/** 高级搜索弹窗关闭处理 */
function handleAdvancedClose() {
  showAdvancedSearch.value = false;
}

/** 重置高级搜索 */
function resetAdvancedSearch() {
  advancedParams.originalPatientId = '';
  advancedParams.examCode = '';
  advancedParams.doctorName = '';
  advancedParams.reportState = '';
  advancedParams.originalExamCode = '';
  advancedParams.modality = '';
  advancedParams.reviewDoctorName = '';
  advancedParams.positive = '';
  advancedParams.infectiousDisease = '';
  advancedParams.writeTime = '';
  advancedParams.auditTime = '';
  advancedParams.syncTime = '';
}

/** 高级搜索 */
function handleAdvancedSearch() {
  // 将高级搜索参数同步到查询参数
  queryParams.value.originalPatientId = advancedParams.originalPatientId;
  queryParams.value.examCode = advancedParams.examCode;
  queryParams.value.doctorName = advancedParams.doctorName;
  queryParams.value.reportState = advancedParams.reportState;
  queryParams.value.originalExamCode = advancedParams.originalExamCode;
  queryParams.value.modality = advancedParams.modality;
  queryParams.value.reviewDoctorName = advancedParams.reviewDoctorName;
  queryParams.value.positive = advancedParams.positive;
  queryParams.value.infectiousDisease = advancedParams.infectiousDisease;
  queryParams.value.writeTime = advancedParams.writeTime;
  queryParams.value.auditTime = advancedParams.auditTime;
  queryParams.value.syncTime = advancedParams.syncTime;

  // 关闭弹窗并执行搜索
  showAdvancedSearch.value = false;
  handleQuery();
}

getList();
</script>

<style scoped>
/* 紧凑搜索栏样式 */
.compact-search-bar {
  background: #fafafa;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.compact-form {
  margin: 0;
}

.compact-form :deep(.el-form-item) {
  margin-right: 16px;
  margin-bottom: 0;
}

.compact-form :deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
  font-size: 14px;
}

.compact-form :deep(.el-button) {
  margin-left: 8px;
}

.compact-form :deep(.el-button--text) {
  color: #409eff;
  padding: 8px 12px;
}

.search-form {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.search-btn-row {
  text-align: left;
  margin-top: 8px;
}

.expand-btn {
  margin-left: 8px;
  color: #606266;
  font-size: 13px;
}

.expand-btn:hover {
  color: #409eff;
}

.expand-icon {
  margin-left: 4px;
  transition: transform 0.3s ease;
}

.expand-icon.is-expanded {
  transform: rotate(180deg);
}

.search-form .el-form-item {
  margin-bottom: 16px;
}

.search-form .el-row:last-child .el-form-item {
  margin-bottom: 0;
}

/* 查看对话框样式 */
.view-form .form-value {
  color: #606266;
  font-size: 14px;
  line-height: 32px;
}

.view-form .form-textarea {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  min-height: 60px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
}

.view-form .el-form-item {
  margin-bottom: 18px;
}
</style>

<template>
  <div class="consultation-notification">
    <!-- 音频元素 -->
    <audio
      ref="notificationAudio"
      :src="soundFile"
      preload="auto"
      @error="handleAudioError"
    ></audio>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ElNotification, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { connectConsultationNotification } from '@/api/consultation/notification'

// 组件属性
const props = defineProps({
  // 是否启用自动连接
  autoConnect: {
    type: Boolean,
    default: true
  },
  // 是否启用音效
  soundEnabled: {
    type: Boolean,
    default: true
  },
  // 是否启用弹窗
  popupEnabled: {
    type: Boolean,
    default: true
  }
})

// 响应式数据
const router = useRouter()
// 暂时禁用音频文件，避免加载错误
const soundFile = ref('')
const notificationAudio = ref(null)

// 连接状态
const eventSource = ref(null)
const isConnected = ref(false)
const reconnectAttempts = ref(0)
const maxReconnectAttempts = 5
const reconnectInterval = ref(null)

// 通知配置
const config = ref({
  enabled: true,
  soundEnabled: true,
  popupEnabled: true,
  volume: 0.8,
  position: 'bottom-right',
  duration: 8000,
  showClose: true
})

// 初始化
onMounted(async () => {
  if (props.autoConnect && config.value.enabled) {
    await connectToNotificationService()
  }
})

// 清理
onUnmounted(() => {
  disconnectFromNotificationService()
})

// 连接到通知服务
const connectToNotificationService = async () => {
  try {
    console.log('正在建立会诊通知SSE连接...')
    
    eventSource.value = connectConsultationNotification()
    
    // 连接成功事件
    eventSource.value.addEventListener('connected', (event) => {
      console.log('会诊通知连接已建立:', event.data)
      isConnected.value = true
      reconnectAttempts.value = 0
      //ElMessage.success('会诊通知连接已建立')
    })
    
    // 接收会诊通知
    eventSource.value.addEventListener('consultation-notification', (event) => {
      console.log('收到会诊通知:', event.data)
      handleConsultationNotification(JSON.parse(event.data))
    })
    
    // 接收广播通知
    eventSource.value.addEventListener('consultation-broadcast', (event) => {
      console.log('收到会诊广播通知:', event.data)
      handleConsultationNotification(JSON.parse(event.data))
    })
    
    // 连接错误处理
    eventSource.value.onerror = (error) => {
      console.error('会诊通知SSE连接错误:', error)
      isConnected.value = false
      
      if (eventSource.value.readyState === EventSource.CLOSED) {
        console.log('会诊通知连接已关闭，尝试重连...')
        attemptReconnect()
      }
    }
    
    // 连接打开事件
    eventSource.value.onopen = (event) => {
      console.log('会诊通知SSE连接已打开:', event)
      isConnected.value = true
    }
    
  } catch (error) {
    console.error('建立会诊通知连接失败:', error)
    ElMessage.error('建立会诊通知连接失败')
  }
}

// 断开连接
const disconnectFromNotificationService = () => {
  if (eventSource.value) {
    eventSource.value.close()
    eventSource.value = null
  }
  
  if (reconnectInterval.value) {
    clearTimeout(reconnectInterval.value)
    reconnectInterval.value = null
  }
  
  isConnected.value = false
  console.log('会诊通知连接已断开')
}

// 尝试重连
const attemptReconnect = () => {
  if (reconnectAttempts.value >= maxReconnectAttempts) {
    console.log('会诊通知重连次数已达上限，停止重连')
    ElMessage.error('会诊通知连接失败，请刷新页面重试')
    return
  }
  
  reconnectAttempts.value++
  const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 30000) // 指数退避，最大30秒
  
  console.log(`会诊通知将在 ${delay}ms 后进行第 ${reconnectAttempts.value} 次重连`)
  
  reconnectInterval.value = setTimeout(() => {
    connectToNotificationService()
  }, delay)
}

// 处理会诊通知
const handleConsultationNotification = (message) => {
  console.log('处理会诊通知:', message)
  
  // 播放提示音
  if (config.value.soundEnabled && props.soundEnabled) {
    playNotificationSound()
  }
  
  // 显示弹窗通知
  if (config.value.popupEnabled && props.popupEnabled) {
    showNotificationPopup(message)
  }
}

// 播放提示音
const playNotificationSound = () => {
  if (notificationAudio.value) {
    try {
      notificationAudio.value.volume = config.value.volume
      notificationAudio.value.currentTime = 0
      notificationAudio.value.play().catch(error => {
        console.warn('播放会诊通知音效失败:', error)
      })
    } catch (error) {
      console.warn('播放会诊通知音效异常:', error)
    }
  }
}

// 显示通知弹窗
const showNotificationPopup = (message) => {
  const notification = ElNotification({
    title: message.title || '会诊通知',
    message: formatNotificationMessage(message),
    type: getNotificationType(message.type),
    position: config.value.position,
    duration: config.value.duration,
    showClose: config.value.showClose,
    dangerouslyUseHTMLString: true,
    customClass: 'consultation-notification-popup',
    onClick: () => {
      handleNotificationClick(message)
      notification.close()
    }
  })
}

// 格式化通知消息
const formatNotificationMessage = (message) => {
  let content = message.content || ''
  
  // 添加详细信息
  if (message.patientName) {
    content += `<br><strong>患者：</strong>${message.patientName}`
  }
  
  if (message.requesterName) {
    content += `<br><strong>申请医生：</strong>${message.requesterName}`
  }
  
  if (message.urgencyLevel) {
    const urgencyText = getUrgencyText(message.urgencyLevel)
    content += `<br><strong>紧急程度：</strong><span style="color: ${getUrgencyColor(message.urgencyLevel)}">${urgencyText}</span>`
  }
  
  if (message.requestNo) {
    content += `<br><strong>申请编号：</strong>${message.requestNo}`
  }
  
  return content
}

// 获取通知类型
const getNotificationType = (type) => {
  const typeMap = {
    'REQUEST': 'warning',
    'ACCEPT': 'success',
    'REJECT': 'error',
    'COMPLETE': 'success'
  }
  return typeMap[type] || 'info'
}

// 获取紧急程度文本
const getUrgencyText = (level) => {
  const levelMap = {
    'URGENT': '紧急',
    'NORMAL': '普通',
    'LOW': '非紧急'
  }
  return levelMap[level] || level
}

// 获取紧急程度颜色
const getUrgencyColor = (level) => {
  const colorMap = {
    'URGENT': '#f56c6c',
    'NORMAL': '#e6a23c',
    'LOW': '#67c23a'
  }
  return colorMap[level] || '#909399'
}

// 处理通知点击
const handleNotificationClick = (message) => {
  console.log('点击会诊通知:', message)
  
  // 根据通知类型跳转到相应页面
  if (message.type === 'REQUEST') {
    // 跳转到我的会诊列表
    router.push('/consultation/consultant')
  } else {
    // 跳转到我的申请列表
    router.push('/consultation/request')
  }
}

// 处理音频错误
const handleAudioError = (error) => {
  console.warn('会诊通知音频加载失败:', error)
}

// 手动重连（供外部调用）
const manualReconnect = () => {
  disconnectFromNotificationService()
  reconnectAttempts.value = 0
  connectToNotificationService()
}

// 暴露方法给父组件
defineExpose({
  connect: connectToNotificationService,
  disconnect: disconnectFromNotificationService,
  reconnect: manualReconnect,
  isConnected: computed(() => isConnected.value)
})
</script>

<style scoped>
.consultation-notification {
  display: none;
}

/* 自定义通知弹窗样式 */
:deep(.consultation-notification-popup) {
  min-width: 350px;
  max-width: 500px;
}

:deep(.consultation-notification-popup .el-notification__content) {
  line-height: 1.6;
}

:deep(.consultation-notification-popup .el-notification__title) {
  font-weight: bold;
  color: #303133;
}
</style>

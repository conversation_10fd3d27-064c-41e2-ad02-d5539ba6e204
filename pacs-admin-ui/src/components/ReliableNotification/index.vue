<template>
  <div class="reliable-notification">
    <!-- 连接状态指示器 -->
    <div v-if="showConnectionStatus" class="connection-status" :class="connectionStatusClass">
      <el-icon><Connection /></el-icon>
      <span>{{ connectionStatusText }}</span>
    </div>

    <!-- 通知弹窗 -->
    <el-notification
      v-for="notification in activeNotifications"
      :key="notification.id"
      :title="notification.title"
      :message="notification.content"
      :type="notification.type || 'info'"
      :duration="notification.duration || 4500"
      :position="notification.position || 'bottom-right'"
      @close="handleNotificationClose(notification)"
    />

    <!-- 离线通知提示 -->
    <el-dialog
      v-model="showOfflineDialog"
      title="离线通知"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="offline-notifications">
        <p>您有 {{ offlineNotifications.length }} 条离线通知：</p>
        <div class="notification-list">
          <div
            v-for="notification in offlineNotifications"
            :key="notification.id"
            class="notification-item"
          >
            <div class="notification-header">
              <span class="notification-title">{{ notification.title }}</span>
              <span class="notification-time">{{ formatTime(notification.createTime) }}</span>
            </div>
            <div class="notification-content">{{ notification.content }}</div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="markAllAsRead">全部标记为已读</el-button>
          <el-button type="primary" @click="showOfflineDialog = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { Connection } from '@element-plus/icons-vue'
import { connectConsultationNotification, acknowledgeNotification, getOfflineNotifications, sendHeartbeatResponse } from '@/api/consultation/reliableNotification'

// 响应式数据
const eventSource = ref(null)
const isConnected = ref(false)
const reconnectAttempts = ref(0)
const maxReconnectAttempts = ref(10)
const reconnectInterval = ref(null)
const activeNotifications = ref([])
const offlineNotifications = ref([])
const showOfflineDialog = ref(false)
const showConnectionStatus = ref(false)
const heartbeatInterval = ref(null)
const lastHeartbeat = ref(Date.now())

// 配置
const config = reactive({
  reconnectDelay: 1000, // 初始重连延迟
  maxReconnectDelay: 30000, // 最大重连延迟
  heartbeatInterval: 30000, // 心跳间隔
  notificationTimeout: 5000, // 通知显示时间
  ackTimeout: 30000, // 确认超时时间
  enableSound: true, // 启用声音提醒
  enableVibration: true // 启用震动提醒（移动端）
})

// 计算属性
const connectionStatusClass = computed(() => ({
  'status-connected': isConnected.value,
  'status-disconnected': !isConnected.value,
  'status-reconnecting': reconnectAttempts.value > 0 && !isConnected.value
}))

const connectionStatusText = computed(() => {
  if (isConnected.value) {
    return '通知连接正常'
  } else if (reconnectAttempts.value > 0) {
    return `正在重连... (${reconnectAttempts.value}/${maxReconnectAttempts.value})`
  } else {
    return '通知连接断开'
  }
})

// 连接到通知服务
const connectToNotificationService = async () => {
  try {
    console.log('建立可靠通知连接...')
    
    eventSource.value = connectConsultationNotification()
    
    // 连接成功事件
    eventSource.value.addEventListener('connected', handleConnected)
    
    // 接收通知
    eventSource.value.addEventListener('consultation-notification', handleNotification)
    
    // 接收广播通知
    eventSource.value.addEventListener('consultation-broadcast', handleBroadcast)
    
    // 心跳响应
    eventSource.value.addEventListener('heartbeat', handleHeartbeat)
    
    // 离线通知
    eventSource.value.addEventListener('offline-notifications', handleOfflineNotifications)
    
    // 连接错误处理
    eventSource.value.onerror = handleConnectionError
    
    // 连接打开事件
    eventSource.value.onopen = handleConnectionOpen
    
  } catch (error) {
    console.error('建立通知连接失败:', error)
    ElMessage.error('建立通知连接失败')
    scheduleReconnect()
  }
}

// 处理连接成功
const handleConnected = (event) => {
  console.log('通知连接已建立:', event.data)
  isConnected.value = true
  reconnectAttempts.value = 0
  showConnectionStatus.value = false
  
  // 启动心跳
  startHeartbeat()
  
  // 请求离线通知
  requestOfflineNotifications()
}

// 处理通知
const handleNotification = (event) => {
  try {
    const notification = JSON.parse(event.data)
    console.log('收到通知:', notification)
    
    // 显示通知
    showNotification(notification)
    
    // 发送确认
    if (notification.notificationId) {
      acknowledgeNotificationReceived(notification.notificationId)
    }
    
    // 播放提示音
    if (config.enableSound) {
      playNotificationSound()
    }
    
    // 震动提醒（移动端）
    if (config.enableVibration && 'vibrate' in navigator) {
      navigator.vibrate([200, 100, 200])
    }
    
  } catch (error) {
    console.error('处理通知失败:', error)
  }
}

// 处理广播通知
const handleBroadcast = (event) => {
  try {
    const notification = JSON.parse(event.data)
    console.log('收到广播通知:', notification)
    showNotification(notification)
  } catch (error) {
    console.error('处理广播通知失败:', error)
  }
}

// 处理心跳
const handleHeartbeat = (event) => {
  try {
    lastHeartbeat.value = Date.now()
    console.debug('收到心跳:', event.data)

    // 发送心跳响应给服务端
    sendHeartbeatResponseToServer()

  } catch (error) {
    console.error('处理心跳失败:', error)
  }
}

// 发送心跳响应给服务端
const sendHeartbeatResponseToServer = async () => {
  try {
    await sendHeartbeatResponse()
    console.debug('心跳响应发送成功')
  } catch (error) {
    console.warn('心跳响应发送失败:', error)
    // 心跳响应失败不影响主要功能，只记录警告
  }
}

// 处理离线通知
const handleOfflineNotifications = (event) => {
  try {
    const notifications = JSON.parse(event.data)
    if (notifications && notifications.length > 0) {
      offlineNotifications.value = notifications
      showOfflineDialog.value = true
      console.log('收到离线通知:', notifications.length, '条')
    }
  } catch (error) {
    console.error('处理离线通知失败:', error)
  }
}

// 处理连接错误
const handleConnectionError = (error) => {
  console.error('通知连接错误:', error)
  isConnected.value = false
  showConnectionStatus.value = true
  
  if (eventSource.value && eventSource.value.readyState === EventSource.CLOSED) {
    console.log('连接已关闭，尝试重连...')
    scheduleReconnect()
  }
}

// 处理连接打开
const handleConnectionOpen = (event) => {
  console.log('通知连接已打开:', event)
  isConnected.value = true
}

// 显示通知
const showNotification = (notification) => {
  const notificationConfig = {
    id: notification.notificationId || Date.now(),
    title: notification.title,
    message: notification.content,
    type: getNotificationType(notification.type),
    duration: config.notificationTimeout,
    onClick: () => handleNotificationClick(notification),
    onClose: () => handleNotificationClose(notification)
  }
  
  // 使用Element Plus的通知组件
  ElNotification(notificationConfig)
  
  // 添加到活动通知列表
  activeNotifications.value.push(notificationConfig)
}

// 获取通知类型
const getNotificationType = (type) => {
  const typeMap = {
    'REQUEST': 'info',
    'ACCEPT': 'success',
    'REJECT': 'warning',
    'COMPLETE': 'success',
    'CANCEL': 'warning',
    'URGENT': 'error'
  }
  return typeMap[type] || 'info'
}

// 处理通知点击
const handleNotificationClick = (notification) => {
  console.log('通知被点击:', notification)
  // 这里可以添加跳转逻辑
  if (notification.url) {
    window.open(notification.url, '_blank')
  }
}

// 处理通知关闭
const handleNotificationClose = (notification) => {
  const index = activeNotifications.value.findIndex(n => n.id === notification.id)
  if (index > -1) {
    activeNotifications.value.splice(index, 1)
  }
}

// 确认通知已收到
const acknowledgeNotificationReceived = async (notificationId) => {
  try {
    await acknowledgeNotification(notificationId)
    console.log('通知确认成功:', notificationId)
  } catch (error) {
    console.error('通知确认失败:', notificationId, error)
  }
}

// 请求离线通知
const requestOfflineNotifications = async () => {
  try {
    const response = await getOfflineNotifications()
    if (response.data && response.data.length > 0) {
      offlineNotifications.value = response.data
      showOfflineDialog.value = true
    }
  } catch (error) {
    console.error('获取离线通知失败:', error)
  }
}

// 标记所有通知为已读
const markAllAsRead = async () => {
  try {
    for (const notification of offlineNotifications.value) {
      if (notification.notificationId) {
        await acknowledgeNotificationReceived(notification.notificationId)
      }
    }
    offlineNotifications.value = []
    showOfflineDialog.value = false
    ElMessage.success('所有通知已标记为已读')
  } catch (error) {
    console.error('标记通知为已读失败:', error)
    ElMessage.error('标记通知为已读失败')
  }
}

// 安排重连
const scheduleReconnect = () => {
  if (reconnectAttempts.value >= maxReconnectAttempts.value) {
    console.error('达到最大重连次数，停止重连')
    ElMessage.error('通知连接失败，请刷新页面重试')
    return
  }
  
  const delay = Math.min(
    config.reconnectDelay * Math.pow(2, reconnectAttempts.value),
    config.maxReconnectDelay
  )
  
  reconnectAttempts.value++
  console.log(`${delay}ms后进行第${reconnectAttempts.value}次重连`)
  
  reconnectInterval.value = setTimeout(() => {
    connectToNotificationService()
  }, delay)
}

// 启动心跳
const startHeartbeat = () => {
  if (heartbeatInterval.value) {
    clearInterval(heartbeatInterval.value)
  }
  
  heartbeatInterval.value = setInterval(() => {
    const now = Date.now()
    if (now - lastHeartbeat.value > config.heartbeatInterval * 2) {
      console.warn('心跳超时，连接可能已断开')
      isConnected.value = false
      scheduleReconnect()
    }
  }, config.heartbeatInterval)
}

// 播放通知声音
const playNotificationSound = () => {
  try {
    const audio = new Audio('/static/sounds/notification.mp3')
    audio.volume = 0.5
    audio.play().catch(error => {
      console.warn('播放通知声音失败:', error)
    })
  } catch (error) {
    console.warn('创建音频对象失败:', error)
  }
}

// 断开连接
const disconnect = () => {
  if (eventSource.value) {
    eventSource.value.close()
    eventSource.value = null
  }
  
  if (reconnectInterval.value) {
    clearTimeout(reconnectInterval.value)
    reconnectInterval.value = null
  }
  
  if (heartbeatInterval.value) {
    clearInterval(heartbeatInterval.value)
    heartbeatInterval.value = null
  }
  
  isConnected.value = false
  console.log('通知连接已断开')
}

// 格式化时间
const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

// 组件挂载
onMounted(() => {
  connectToNotificationService()
})

// 组件卸载
onBeforeUnmount(() => {
  disconnect()
})

// 暴露方法给父组件
defineExpose({
  connect: connectToNotificationService,
  disconnect,
  isConnected: () => isConnected.value,
  reconnect: () => {
    disconnect()
    setTimeout(connectToNotificationService, 1000)
  }
})
</script>

<style scoped>
.reliable-notification {
  position: relative;
}

.connection-status {
  position: fixed;
  top: 60px;
  right: 20px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 2000;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.status-connected {
  background-color: #f0f9ff;
  color: #10b981;
  border: 1px solid #10b981;
}

.status-disconnected {
  background-color: #fef2f2;
  color: #ef4444;
  border: 1px solid #ef4444;
}

.status-reconnecting {
  background-color: #fffbeb;
  color: #f59e0b;
  border: 1px solid #f59e0b;
}

.offline-notifications {
  max-height: 400px;
  overflow-y: auto;
}

.notification-list {
  margin-top: 16px;
}

.notification-item {
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #f9fafb;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notification-title {
  font-weight: 500;
  color: #374151;
}

.notification-time {
  font-size: 12px;
  color: #6b7280;
}

.notification-content {
  color: #4b5563;
  font-size: 14px;
  line-height: 1.4;
}
</style>
